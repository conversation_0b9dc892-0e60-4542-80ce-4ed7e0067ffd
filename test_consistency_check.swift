import Foundation

// Test om te controleren dat MatchDetailView en PlayerDetailView nu consistent zijn
// Beide moeten nu alleen de opgeslagen ELO changes gebruiken

print("=== CONSISTENCY CHECK ===")
print("<PERSON><PERSON> views moeten nu alleen opgeslagen ELO changes gebruiken")
print("Geen fallback berekeningen meer!")

// Simuleer een match met opgeslagen ELO changes
struct MockMatch {
    let id = UUID()
    let eloChanges: [UUID: Double]
    let status: String = "completed"
}

struct MockPlayer {
    let id: UUID
    let name: String
    let eloRating: Double
}

// Test scenario
let joost = MockPlayer(id: UUID(), name: "<PERSON><PERSON>", eloRating: 1300)
let richard = MockPlayer(id: UUID(), name: "<PERSON>", eloRating: 1100)
let darius = MockPlayer(id: UUID(), name: "<PERSON>", eloRating: 1250)
let gavin = MockPlayer(id: UUID(), name: "<PERSON>", eloRating: 1150)

// Simuleer opgeslagen ELO changes (zoals berekend door DataManager)
let storedEloChanges: [UUID: Double] = [
    joost.id: +2.75,    // Joost won maar kreeg minder omdat Richard lagere ELO heeft
    richard.id: +3.25,  // Richard won en kreeg meer omdat Joost hogere ELO heeft
    darius.id: -3.12,   // Darius verloor en verloor meer omdat hij hogere ELO heeft
    gavin.id: -2.88     // Gavin verloor maar verloor minder omdat hij lagere ELO heeft
]

let match = MockMatch(eloChanges: storedEloChanges)

print("\nOpgeslagen ELO changes in match:")
let allPlayers: [MockPlayer] = [joost, richard, darius, gavin]
for (playerId, change) in storedEloChanges {
    let playerName = allPlayers.first(where: { $0.id == playerId })?.name ?? "Unknown"
    let sign = change >= 0 ? "+" : ""
    print("  \(playerName): \(sign)\(String(format: "%.2f", change))")
}

// Simuleer wat beide views nu zouden doen
func simulateMatchDetailView(player: MockPlayer, match: MockMatch) -> Double {
    // Nieuwe logica: gebruik ALTIJD opgeslagen ELO change
    if let storedChange = match.eloChanges[player.id] {
        return storedChange
    }
    print("⚠️ Geen opgeslagen ELO change gevonden voor speler \(player.name)")
    return 0.0
}

func simulatePlayerDetailView(player: MockPlayer, match: MockMatch) -> Double {
    // Nieuwe logica: gebruik ALTIJD opgeslagen ELO change
    if let storedChange = match.eloChanges[player.id] {
        return storedChange
    }
    print("⚠️ Geen opgeslagen ELO change gevonden voor speler \(player.name)")
    return 0.0
}

print("\n" + String(repeating: "=", count: 60))
print("CONSISTENCY TEST RESULTS")
print(String(repeating: "=", count: 60))

let players = [joost, richard, darius, gavin]

for player in players {
    let matchDetailResult = simulateMatchDetailView(player: player, match: match)
    let playerDetailResult = simulatePlayerDetailView(player: player, match: match)
    
    let consistent = abs(matchDetailResult - playerDetailResult) < 0.001
    let status = consistent ? "✅ CONSISTENT" : "❌ INCONSISTENT"
    
    print("\(player.name):")
    print("  MatchDetailView: \(String(format: "%.2f", matchDetailResult))")
    print("  PlayerDetailView: \(String(format: "%.2f", playerDetailResult))")
    print("  Status: \(status)")
    print()
}

print(String(repeating: "=", count: 60))
print("CONCLUSIE:")
print("✅ Beide views gebruiken nu alleen opgeslagen ELO changes")
print("✅ Geen fallback berekeningen meer")
print("✅ Consistentie gegarandeerd")
print("✅ DataManager is de enige bron van waarheid voor ELO berekeningen")

print("\nVOLGENDE STAPPEN:")
print("1. Test de app met een nieuwe Mix & Match wedstrijd")
print("2. Controleer dat beide views dezelfde ELO changes tonen")
print("3. Als er nog inconsistenties zijn, check of DataManager correct opslaat")

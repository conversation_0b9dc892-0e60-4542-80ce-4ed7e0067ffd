import Foundation
import SwiftUI

// Conditional import for flic2lib - only available on real devices
#if !targetEnvironment(simulator)
import flic2lib
#endif

/// Enum voor Flic button types
enum FlicButtonType: String, CaseIterable {
    case team1 = "team1"
    case team2 = "team2"
    
    var displayName: String {
        switch self {
        case .team1:
            return "Speler/Team 1"
        case .team2:
            return "Speler/Team 2"
        }
    }
    
    var icon: String {
        switch self {
        case .team1:
            return "1.circle.fill"
        case .team2:
            return "2.circle.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .team1:
            return .blue
        case .team2:
            return .red
        }
    }
}

/// Status van een Flic button connectie
enum FlicConnectionStatus {
    case disconnected
    case connecting
    case connected
    case failed(String)
    
    var displayText: String {
        switch self {
        case .disconnected:
            return "Niet verbonden"
        case .connecting:
            return "Verbinden..."
        case .connected:
            return "Verbonden"
        case .failed(let error):
            return "Fout: \(error)"
        }
    }
    
    var color: Color {
        switch self {
        case .disconnected:
            return .secondary
        case .connecting:
            return .orange
        case .connected:
            return .green
        case .failed:
            return .red
        }
    }
}

/// Manager voor Flic button integratie
class FlicManager: NSObject, ObservableObject {
    @Published var team1Status: FlicConnectionStatus = .disconnected
    @Published var team2Status: FlicConnectionStatus = .disconnected
    @Published var team1ButtonName: String = ""
    @Published var team2ButtonName: String = ""

    private let userDefaults = UserDefaults.standard
    private let team1ButtonKey = "flicTeam1Button"
    private let team2ButtonKey = "flicTeam2Button"
    private let team1ButtonUUIDKey = "flicTeam1ButtonUUID"
    private let team2ButtonUUIDKey = "flicTeam2ButtonUUID"

    // Callback voor button presses tijdens live matches
    var onButtonPress: ((FlicButtonType) -> Void)?
    var onButtonDoublePress: ((FlicButtonType) -> Void)?

    // Debounce mechanism to prevent multiple rapid button presses
    private var lastButtonPressTime: [FlicButtonType: Date] = [:]
    private let buttonPressDebounceInterval: TimeInterval = 0.5 // 500ms debounce

    // Live match state
    @Published var isLiveMatchActive: Bool = false
    @Published var currentMatchId: UUID?

    // Flic2 SDK properties
    #if !targetEnvironment(simulator)
    private var team1ButtonUUID: UUID?
    private var team2ButtonUUID: UUID?
    private var pendingConnectionType: FlicButtonType?
    #endif
    
    override init() {
        super.init()
        loadSavedButtons()
        setupFlicSDK()
        print("🔘 FlicManager initialized")
    }

    /// Setup Flic2 SDK
    private func setupFlicSDK() {
        #if !targetEnvironment(simulator)
        // Configure Flic2 SDK
        _ = FLICManager.configure(with: self, buttonDelegate: self, background: false)
        print("🔘 Flic2 SDK configured")

        // Check existing buttons
        checkExistingButtons()
        #else
        print("🔘 Flic2 SDK not available in simulator - using simulation mode")
        #endif
    }
    
    /// Laad opgeslagen button configuraties
    private func loadSavedButtons() {
        team1ButtonName = userDefaults.string(forKey: team1ButtonKey) ?? ""
        team2ButtonName = userDefaults.string(forKey: team2ButtonKey) ?? ""

        #if !targetEnvironment(simulator)
        // Load UUIDs
        if let team1UUIDString = userDefaults.string(forKey: team1ButtonUUIDKey) {
            team1ButtonUUID = UUID(uuidString: team1UUIDString)
        }
        if let team2UUIDString = userDefaults.string(forKey: team2ButtonUUIDKey) {
            team2ButtonUUID = UUID(uuidString: team2UUIDString)
        }
        #endif

        // Status will be updated after SDK initialization
        print("🔘 Loaded saved buttons - Team1: \(team1ButtonName), Team2: \(team2ButtonName)")
    }
    
    /// Start het connectie proces voor een specifieke button
    func connectButton(type: FlicButtonType) {
        print("🔘 Starting connection for \(type.displayName)")

        switch type {
        case .team1:
            team1Status = .connecting
        case .team2:
            team2Status = .connecting
        }

        #if !targetEnvironment(simulator)
        // Store which button type we're connecting
        pendingConnectionType = type

        // Start Flic2 scan for new buttons
        FLICManager.shared()?.scanForButtons(stateChangeHandler: { event in
            DispatchQueue.main.async {
                print("🔘 Scan status: \(event.rawValue)")
                switch type {
                case .team1:
                    self.team1Status = .connecting
                case .team2:
                    self.team2Status = .connecting
                }
            }
        }, completion: { button, error in
            DispatchQueue.main.async {
                if let error = error {
                    print("🔘 Scan failed: \(error)")
                    switch type {
                    case .team1:
                        self.team1Status = .failed(error.localizedDescription)
                    case .team2:
                        self.team2Status = .failed(error.localizedDescription)
                    }
                    self.pendingConnectionType = nil
                } else if let button = button {
                    print("🔘 Button discovered and connected: \(button.name ?? "Unknown")")
                    self.handleNewButtonConnected(button: button, type: type)
                }
            }
        })
        print("🔘 Started scanning for Flic buttons")
        #else
        // Fallback to simulation for testing in simulator
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.simulateConnection(type: type)
        }
        #endif
    }
    
    /// Verbreek de connectie voor een specifieke button
    func disconnectButton(type: FlicButtonType) {
        print("🔘 Disconnecting \(type.displayName)")

        #if !targetEnvironment(simulator)
        // Remove from Flic2 SDK
        let buttonUUID: UUID?
        switch type {
        case .team1:
            buttonUUID = team1ButtonUUID
        case .team2:
            buttonUUID = team2ButtonUUID
        }

        if let uuid = buttonUUID,
           let flicButton = FLICManager.shared()?.buttons().first(where: { $0.identifier == uuid }) {
            FLICManager.shared()?.forgetButton(flicButton) { buttonUUID, error in
                if let error = error {
                    print("🔘 Failed to remove button: \(error)")
                } else {
                    print("🔘 Removed button from Flic SDK: \(buttonUUID)")
                }
            }
        }
        #endif

        // Update local state
        switch type {
        case .team1:
            team1Status = .disconnected
            team1ButtonName = ""
            userDefaults.removeObject(forKey: team1ButtonKey)
            userDefaults.removeObject(forKey: team1ButtonUUIDKey)
            #if !targetEnvironment(simulator)
            team1ButtonUUID = nil
            #endif
        case .team2:
            team2Status = .disconnected
            team2ButtonName = ""
            userDefaults.removeObject(forKey: team2ButtonKey)
            userDefaults.removeObject(forKey: team2ButtonUUIDKey)
            #if !targetEnvironment(simulator)
            team2ButtonUUID = nil
            #endif
        }
    }

    #if !targetEnvironment(simulator)
    /// Handle new button connected
    private func handleNewButtonConnected(button: FLICButton, type: FlicButtonType) {
        let buttonId = button.identifier
        let buttonName = button.name ?? "Flic Button"

        // Set up button delegate
        button.delegate = self

        // Configure trigger mode to support single click, double click and hold
        button.triggerMode = .clickAndDoubleClickAndHold

        // Save button configuration
        switch type {
        case .team1:
            team1Status = .connected
            team1ButtonName = buttonName
            team1ButtonUUID = buttonId
            userDefaults.set(buttonName, forKey: team1ButtonKey)
            userDefaults.set(buttonId.uuidString, forKey: team1ButtonUUIDKey)
        case .team2:
            team2Status = .connected
            team2ButtonName = buttonName
            team2ButtonUUID = buttonId
            userDefaults.set(buttonName, forKey: team2ButtonKey)
            userDefaults.set(buttonId.uuidString, forKey: team2ButtonUUIDKey)
        }

        pendingConnectionType = nil
        print("🔘 Successfully configured \(buttonName) for \(type.displayName)")
    }
    #endif


    
    /// Handle button press events
    func handleButtonPress(type: FlicButtonType) {
        print("🔘 Button pressed: \(type.displayName)")
        print("🔘 Device: \(UIDevice.current.name)")
        print("🔘 App running on: \(UIDevice.current.userInterfaceIdiom == .pad ? "iPad" : "iPhone")")

        // Only process button presses during live matches
        guard isLiveMatchActive else {
            print("🔘 Button press ignored - no active live match")
            return
        }

        // Debounce mechanism to prevent multiple rapid button presses
        let now = Date()
        if let lastPressTime = lastButtonPressTime[type],
           now.timeIntervalSince(lastPressTime) < buttonPressDebounceInterval {
            print("🔘 Button press ignored - too soon after last press (debounce)")
            return
        }

        lastButtonPressTime[type] = now
        print("🔘 Button press accepted - calling callback")
        onButtonPress?(type)
    }

    /// Handle button double press events
    func handleButtonDoublePress(type: FlicButtonType) {
        print("🔘 Button double pressed: \(type.displayName)")
        print("🔘 Device: \(UIDevice.current.name)")
        print("🔘 App running on: \(UIDevice.current.userInterfaceIdiom == .pad ? "iPad" : "iPhone")")

        // Only process button presses during live matches
        guard isLiveMatchActive else {
            print("🔘 Button double press ignored - no active live match")
            return
        }

        // Debounce mechanism to prevent multiple rapid button presses
        let now = Date()
        if let lastPressTime = lastButtonPressTime[type],
           now.timeIntervalSince(lastPressTime) < buttonPressDebounceInterval {
            print("🔘 Button double press ignored - too soon after last press (debounce)")
            return
        }

        lastButtonPressTime[type] = now
        print("🔘 Button double press accepted - calling callback")
        onButtonDoublePress?(type)
    }

    /// Start live match mode
    func startLiveMatch(matchId: UUID) {
        isLiveMatchActive = true
        currentMatchId = matchId

        // Reset debounce state for new match
        lastButtonPressTime.removeAll()

        // Debug info
        print("🔘 Live match started - Flic buttons active for match: \(matchId)")
        print("🔘 Device: \(UIDevice.current.name)")
        print("🔘 Team 1 configured: \(!team1ButtonName.isEmpty)")
        print("🔘 Team 2 configured: \(!team2ButtonName.isEmpty)")
    }

    /// Stop live match mode
    func stopLiveMatch() {
        isLiveMatchActive = false
        currentMatchId = nil
        onButtonPress = nil
        onButtonDoublePress = nil

        // Clear debounce state
        lastButtonPressTime.removeAll()

        print("🔘 Live match stopped - Flic buttons deactivated")
    }
    
    /// Check of beide buttons verbonden zijn
    var bothButtonsConnected: Bool {
        if case .connected = team1Status,
           case .connected = team2Status {
            return true
        }
        return false
    }
    
    /// Reset alle button connecties
    func resetAllConnections() {
        disconnectButton(type: .team1)
        disconnectButton(type: .team2)
        print("🔘 All Flic connections reset")
    }

    /// Check and log current connection status
    func checkConnectionStatus() {
        print("🔘 === Connection Status Check ===")
        print("🔘 Device: \(UIDevice.current.name)")
        print("🔘 Device Type: \(UIDevice.current.userInterfaceIdiom == .pad ? "iPad" : "iPhone")")
        print("🔘 Team 1 Status: \(team1Status.displayText)")
        print("🔘 Team 1 Button: \(team1ButtonName.isEmpty ? "Not configured" : team1ButtonName)")
        print("🔘 Team 2 Status: \(team2Status.displayText)")
        print("🔘 Team 2 Button: \(team2ButtonName.isEmpty ? "Not configured" : team2ButtonName)")
        print("🔘 Live Match Active: \(isLiveMatchActive)")
        if let matchId = currentMatchId {
            print("🔘 Current Match ID: \(matchId)")
        }
        print("🔘 ==============================")
    }

    // MARK: - Flic2 SDK Helper Methods

    #if !targetEnvironment(simulator)
    /// Check existing buttons in Flic2 SDK
    private func checkExistingButtons() {
        guard let manager = FLICManager.shared() else {
            print("🔘 FLICManager not available")
            return
        }

        let knownButtons = manager.buttons()
        print("🔘 Found \(knownButtons.count) known Flic buttons")

        for button in knownButtons {
            let buttonId = button.identifier
            let buttonName = button.name ?? "Flic Button"

            print("🔘 Checking button: \(buttonName) (\(buttonId))")

            // Check if this button matches our saved UUIDs
            if buttonId == team1ButtonUUID {
                team1Status = button.state == .connected ? .connected : .disconnected
                team1ButtonName = buttonName
                print("🔘 Found Team 1 button: \(buttonName)")
            } else if buttonId == team2ButtonUUID {
                team2Status = button.state == .connected ? .connected : .disconnected
                team2ButtonName = buttonName
                print("🔘 Found Team 2 button: \(buttonName)")
            }

            // Set up button delegate
            button.delegate = self

            // Configure trigger mode to support single click, double click and hold
            button.triggerMode = .clickAndDoubleClickAndHold
        }
    }

    /// Stop scanning for buttons
    func stopScanning() {
        FLICManager.shared()?.stopScan()
        print("🔘 Stopped scanning for buttons")
    }
    #else
    /// Check existing buttons - simulator fallback
    private func checkExistingButtons() {
        print("🔘 Simulator mode - no existing buttons to check")
    }

    /// Stop scanning for buttons - simulator fallback
    func stopScanning() {
        print("🔘 Simulator mode - no scanning to stop")
    }

    /// Simulate connection for testing in simulator
    private func simulateConnection(type: FlicButtonType) {
        let buttonName = "Simulated \(type.displayName) Button"

        switch type {
        case .team1:
            team1Status = .connected
            team1ButtonName = buttonName
            userDefaults.set(buttonName, forKey: team1ButtonKey)
        case .team2:
            team2Status = .connected
            team2ButtonName = buttonName
            userDefaults.set(buttonName, forKey: team2ButtonKey)
        }

        print("🔘 Simulated connection for \(buttonName)")
    }
    #endif
}

// MARK: - Flic2 SDK Delegates

#if !targetEnvironment(simulator)
extension FlicManager: FLICManagerDelegate {
    func managerDidRestoreState(_ manager: FLICManager) {
        print("🔘 FLICManager state restored")
        checkExistingButtons()
    }

    func manager(_ manager: FLICManager, didUpdate state: FLICManagerState) {
        print("🔘 FLICManager state updated: \(state.rawValue)")
    }
}

extension FlicManager: FLICButtonDelegate {
    // Required delegate methods
    func buttonDidConnect(_ button: FLICButton) {
        print("🔘 Button connected: \(button.name ?? "Unknown")")
    }

    func buttonIsReady(_ button: FLICButton) {
        print("🔘 Button is ready: \(button.name ?? "Unknown")")

        let buttonId = button.identifier
        DispatchQueue.main.async {
            if buttonId == self.team1ButtonUUID {
                self.team1Status = .connected
            } else if buttonId == self.team2ButtonUUID {
                self.team2Status = .connected
            }
        }
    }

    func button(_ button: FLICButton, didDisconnectWithError error: Error?) {
        print("🔘 Button disconnected: \(button.name ?? "Unknown"), error: \(error?.localizedDescription ?? "none")")

        let buttonId = button.identifier
        DispatchQueue.main.async {
            if buttonId == self.team1ButtonUUID {
                self.team1Status = .disconnected
            } else if buttonId == self.team2ButtonUUID {
                self.team2Status = .disconnected
            }
        }
    }

    func button(_ button: FLICButton, didFailToConnectWithError error: Error?) {
        print("🔘 Button failed to connect: \(button.name ?? "Unknown"), error: \(error?.localizedDescription ?? "none")")

        let buttonId = button.identifier
        DispatchQueue.main.async {
            if buttonId == self.team1ButtonUUID {
                self.team1Status = .failed(error?.localizedDescription ?? "Connection failed")
            } else if buttonId == self.team2ButtonUUID {
                self.team2Status = .failed(error?.localizedDescription ?? "Connection failed")
            }
        }
    }

    // Optional delegate methods for button events
    func button(_ button: FLICButton, didReceiveButtonClick queued: Bool, age: Int) {
        print("🔘 Flic button clicked: \(button.name ?? "Unknown")")

        // Determine which team this button belongs to
        let buttonId = button.identifier
        let buttonType: FlicButtonType?

        if buttonId == team1ButtonUUID {
            buttonType = .team1
        } else if buttonId == team2ButtonUUID {
            buttonType = .team2
        } else {
            buttonType = nil
        }

        guard let type = buttonType else {
            print("🔘 Unknown button clicked - ignoring")
            return
        }

        // Handle the button press
        DispatchQueue.main.async {
            self.handleButtonPress(type: type)
        }
    }

    func button(_ button: FLICButton, didReceiveButtonDoubleClick queued: Bool, age: Int) {
        print("🔘 Flic button double clicked: \(button.name ?? "Unknown")")

        // Determine which team this button belongs to
        let buttonId = button.identifier
        let buttonType: FlicButtonType?

        if buttonId == team1ButtonUUID {
            buttonType = .team1
        } else if buttonId == team2ButtonUUID {
            buttonType = .team2
        } else {
            buttonType = nil
        }

        guard let type = buttonType else {
            print("🔘 Unknown button double clicked - ignoring")
            return
        }

        // Handle the button double press
        DispatchQueue.main.async {
            self.handleButtonDoublePress(type: type)
        }
    }
}
#endif

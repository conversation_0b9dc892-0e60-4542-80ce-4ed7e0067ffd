import AVFoundation
import Foundation

class SoundManager: ObservableObject {
    static let shared = SoundManager()

    private var pointPlayer: AVAudioPlayer?
    private var gameWinPlayer: AVAudioPlayer?
    private var matchWinPlayer: AVAudioPlayer?

    // Speech synthesizer for announcements
    private var speechSynthesizer: AVSpeechSynthesizer?

    // Mix & Match announcer setting
    @Published var isMixMatchAnnouncerEnabled: Bool {
        didSet {
            UserDefaults.standard.set(isMixMatchAnnouncerEnabled, forKey: "mixMatchAnnouncerEnabled")
        }
    }

    private let userDefaults = UserDefaults.standard

    private init() {
        // Load announcer setting
        self.isMixMatchAnnouncerEnabled = userDefaults.bool(forKey: "mixMatchAnnouncerEnabled")
        print("🎙️ SoundManager initialized - Mix & Match announcer enabled: \(self.isMixMatchAnnouncerEnabled)")

        setupAudioSession()
        setupAudioPlayers()
    }
    
    private func setupAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default, options: [.mixWithOthers])
            try AVAudioSession.sharedInstance().setActive(true)
            print("🔊 Audio session setup successful")
        } catch {
            print("🔊 Failed to setup audio session: \(error)")
        }
    }

    private func setupAudioPlayers() {
        // Setup point sound player
        if let pointURL = Bundle.main.url(forResource: "point", withExtension: "wav") {
            do {
                pointPlayer = try AVAudioPlayer(contentsOf: pointURL)
                pointPlayer?.prepareToPlay()
                print("🔊 Point sound loaded successfully")
            } catch {
                print("🔊 Failed to load point sound: \(error)")
            }
        } else {
            print("🔊 Point sound file not found")
        }

        // Setup game win sound player
        if let gameWinURL = Bundle.main.url(forResource: "game_win", withExtension: "wav") {
            do {
                gameWinPlayer = try AVAudioPlayer(contentsOf: gameWinURL)
                gameWinPlayer?.prepareToPlay()
                print("🔊 Game win sound loaded successfully")
            } catch {
                print("🔊 Failed to load game win sound: \(error)")
            }
        } else {
            print("🔊 Game win sound file not found")
        }

        // Setup match win sound player
        if let matchWinURL = Bundle.main.url(forResource: "match_win", withExtension: "wav") {
            do {
                matchWinPlayer = try AVAudioPlayer(contentsOf: matchWinURL)
                matchWinPlayer?.prepareToPlay()
                print("🔊 Match win sound loaded successfully")
            } catch {
                print("🔊 Failed to load match win sound: \(error)")
            }
        } else {
            print("🔊 Match win sound file not found")
        }
    }

    func playPointSound() {
        print("🔊 Playing point sound (WAV)...")

        guard let player = pointPlayer else {
            print("🔊 Point player not available")
            return
        }

        // Ensure audio session is active
        ensureAudioSessionActive()

        // Reset to beginning and play
        player.currentTime = 0
        let success = player.play()

        print("🔊 Point sound play result: \(success)")
    }
    
    func playGameWinSound() {
        print("🔊 Playing game win sound (WAV)...")

        guard let player = gameWinPlayer else {
            print("🔊 Game win player not available")
            return
        }

        ensureAudioSessionActive()

        // Reset to beginning and play
        player.currentTime = 0
        let success = player.play()

        print("🔊 Game win sound play result: \(success)")
    }

    func playMatchWinSound() {
        print("🔊 Playing match win sound (WAV)...")

        guard let player = matchWinPlayer else {
            print("🔊 Match win player not available")
            return
        }

        ensureAudioSessionActive()

        // Reset to beginning and play
        player.currentTime = 0
        let success = player.play()

        print("🔊 Match win sound play result: \(success)")
    }

    // MARK: - Mix & Match Announcer

    /// Announces the next teams for Mix & Match games using text-to-speech
    func announceMixMatchTeams(team1Players: [Player], team2Players: [Player]) {
        print("🎙️ announceMixMatchTeams called - enabled: \(isMixMatchAnnouncerEnabled)")

        guard isMixMatchAnnouncerEnabled else {
            print("🎙️ Mix & Match announcer is disabled")
            return
        }

        print("🎙️ Announcing Mix & Match teams...")

        let team1Names = team1Players.map { $0.announcerName }.joined(separator: " and ")
        let team2Names = team2Players.map { $0.announcerName }.joined(separator: " and ")

        let announcement = "Next game: \(team1Names) versus \(team2Names)"
        print("🎙️ Announcement text: \(announcement)")

        // Ensure audio session is configured for speech
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback, mode: .spokenAudio, options: [.duckOthers])
            try AVAudioSession.sharedInstance().setActive(true)
            print("🎙️ Audio session configured for speech")
        } catch {
            print("🎙️ Failed to configure audio session for speech: \(error)")
        }

        // Initialize synthesizer if needed
        if speechSynthesizer == nil {
            speechSynthesizer = AVSpeechSynthesizer()
        }

        guard let synthesizer = speechSynthesizer else {
            print("🎙️ Failed to create speech synthesizer")
            return
        }

        let utterance = AVSpeechUtterance(string: announcement)

        // Configure speech settings
        utterance.rate = 0.45 // Slightly slower for clarity
        utterance.volume = 1.0 // Maximum volume
        utterance.pitchMultiplier = 1.0

        // Use English voice for consistency
        utterance.voice = AVSpeechSynthesisVoice(language: "en-US")

        // Add debug info about the voice
        if let voice = utterance.voice {
            print("🎙️ Using voice: \(voice.name) (\(voice.language))")
        } else {
            print("🎙️ Warning: No voice selected, using default")
        }

        // Check if synthesizer is busy
        if synthesizer.isSpeaking {
            print("🎙️ Synthesizer is busy, stopping current speech")
            synthesizer.stopSpeaking(at: .immediate)
        }

        synthesizer.speak(utterance)

        print("🎙️ Speech synthesis started for: \(announcement)")
    }

    private func ensureAudioSessionActive() {
        do {
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            print("🔊 Failed to activate audio session: \(error)")
        }
    }
}

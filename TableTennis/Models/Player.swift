import Foundation
import CloudKit

/// Model voor een tafeltennis speler
struct Player: Identifiable, Hashable {
    let id: UUID
    var name: String
    var announcerName: String // Naam gebruikt door de announcer (default: name)
    var eloRating: Double
    var matchesPlayed: Int
    var matchesWon: Int
    var gamesPlayed: Int
    var gamesWon: Int
    var createdAt: Date
    var competitionId: UUID // Referentie naar de competitie
    var profileImageData: Data? // Optionele profielfoto als Data
    var cutoutImageData: Data? // Optionele cut-out versie van profielfoto

    init(name: String, competitionId: UUID, eloRating: Double = 1200.0, profileImageData: Data? = nil, cutoutImageData: Data? = nil, announcerName: String? = nil) {
        self.id = UUID()
        self.name = name
        self.announcerName = announcerName ?? name // Default to regular name if not provided
        self.competitionId = competitionId
        self.eloRating = eloRating
        self.matchesPlayed = 0
        self.matchesWon = 0
        self.gamesPlayed = 0
        self.gamesWon = 0
        self.createdAt = Date()
        self.profileImageData = profileImageData
        self.cutoutImageData = cutoutImageData
    }
    
    /// Berekent het win percentage voor wedstrijden
    var matchWinPercentage: Double {
        guard matchesPlayed > 0 else { return 0.0 }
        return Double(matchesWon) / Double(matchesPlayed) * 100.0
    }
    
    /// Berekent het win percentage voor games
    var gameWinPercentage: Double {
        guard gamesPlayed > 0 else { return 0.0 }
        return Double(gamesWon) / Double(gamesPlayed) * 100.0
    }
    
    /// Berekent het aantal verloren wedstrijden
    var matchesLost: Int {
        return matchesPlayed - matchesWon
    }
    
    /// Berekent het aantal verloren games
    var gamesLost: Int {
        return gamesPlayed - gamesWon
    }
}

/// Extensie voor ELO berekeningen
extension Player {
    /// Berekent de verwachte score tegen een andere speler
    func expectedScore(against opponent: Player) -> Double {
        let ratingDifference = opponent.eloRating - self.eloRating
        return 1.0 / (1.0 + pow(10.0, ratingDifference / 400.0))
    }

    /// Berekent de nieuwe ELO rating na een wedstrijd
    func newEloRating(against opponent: Player, actualScore: Double, kFactor: Double = 32.0) -> Double {
        let expectedScore = self.expectedScore(against: opponent)
        return self.eloRating + kFactor * (actualScore - expectedScore)
    }
}

// MARK: - CloudKit Support
extension Player {
    static let recordType = "Player"

    /// Initializer vanuit CloudKit CKRecord
    init?(from record: CKRecord) {
        guard let name = record["name"] as? String,
              let competitionIdString = record["competitionId"] as? String,
              let competitionId = UUID(uuidString: competitionIdString),
              let eloRating = record["eloRating"] as? Double,
              let matchesPlayed = record["matchesPlayed"] as? Int,
              let matchesWon = record["matchesWon"] as? Int,
              let gamesPlayed = record["gamesPlayed"] as? Int,
              let gamesWon = record["gamesWon"] as? Int,
              let createdAt = record["createdAt"] as? Date,
              let idString = record.recordID.recordName.components(separatedBy: "_").last,
              let id = UUID(uuidString: idString) else {
            return nil
        }

        self.id = id
        self.name = name
        // Load announcer name from CloudKit, fallback to regular name if not present
        self.announcerName = record["announcerName"] as? String ?? name
        self.competitionId = competitionId
        self.eloRating = eloRating
        self.matchesPlayed = matchesPlayed
        self.matchesWon = matchesWon
        self.gamesPlayed = gamesPlayed
        self.gamesWon = gamesWon
        self.createdAt = createdAt

        // Optionele profielfoto uit CloudKit
        if let asset = record["profileImage"] as? CKAsset,
           let data = try? Data(contentsOf: asset.fileURL!) {
            self.profileImageData = data
        } else {
            self.profileImageData = nil
        }

        // Optionele cut-out foto uit CloudKit
        if let asset = record["cutoutImage"] as? CKAsset,
           let data = try? Data(contentsOf: asset.fileURL!) {
            self.cutoutImageData = data
        } else {
            self.cutoutImageData = nil
        }
    }

    /// Converteert naar CloudKit CKRecord
    func toCKRecord() -> CKRecord {
        let recordID = CKRecord.ID(recordName: "Player_\(id.uuidString)")
        let record = CKRecord(recordType: Player.recordType, recordID: recordID)

        record["name"] = name
        record["announcerName"] = announcerName
        record["competitionId"] = competitionId.uuidString
        record["eloRating"] = eloRating
        record["matchesPlayed"] = matchesPlayed
        record["matchesWon"] = matchesWon
        record["gamesPlayed"] = gamesPlayed
        record["gamesWon"] = gamesWon
        record["createdAt"] = createdAt

        // Voeg profielfoto toe als CKAsset
        if let imageData = profileImageData {
            let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent("\(id.uuidString)_profile.jpg")
            do {
                try imageData.write(to: tempURL)
                let asset = CKAsset(fileURL: tempURL)
                record["profileImage"] = asset
            } catch {
                print("Error writing profile image to temp file: \(error)")
            }
        }

        // Voeg cut-out foto toe als CKAsset
        if let cutoutData = cutoutImageData {
            let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent("\(id.uuidString)_cutout.png")
            do {
                try cutoutData.write(to: tempURL)
                let asset = CKAsset(fileURL: tempURL)
                record["cutoutImage"] = asset
            } catch {
                print("Error writing cutout image to temp file: \(error)")
            }
        }

        return record
    }

    /// Converteert naar CloudKit CKRecord met bestaand record (voor updates)
    func toCKRecord(existingRecord: CKRecord) -> CKRecord {
        existingRecord["name"] = name
        existingRecord["announcerName"] = announcerName
        existingRecord["competitionId"] = competitionId.uuidString
        existingRecord["eloRating"] = eloRating
        existingRecord["matchesPlayed"] = matchesPlayed
        existingRecord["matchesWon"] = matchesWon
        existingRecord["gamesPlayed"] = gamesPlayed
        existingRecord["gamesWon"] = gamesWon
        existingRecord["createdAt"] = createdAt

        // Update profielfoto als CKAsset
        if let imageData = profileImageData {
            let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent("\(id.uuidString)_profile.jpg")
            do {
                try imageData.write(to: tempURL)
                let asset = CKAsset(fileURL: tempURL)
                existingRecord["profileImage"] = asset
            } catch {
                print("Error writing profile image to temp file: \(error)")
            }
        } else {
            // Verwijder profielfoto als deze nil is
            existingRecord["profileImage"] = nil
        }

        // Update cut-out foto als CKAsset
        if let cutoutData = cutoutImageData {
            let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent("\(id.uuidString)_cutout.png")
            do {
                try cutoutData.write(to: tempURL)
                let asset = CKAsset(fileURL: tempURL)
                existingRecord["cutoutImage"] = asset
            } catch {
                print("Error writing cutout image to temp file: \(error)")
            }
        } else {
            // Verwijder cut-out foto als deze nil is
            existingRecord["cutoutImage"] = nil
        }

        return existingRecord
    }
}

// MARK: - Codable Implementation
extension Player: Codable {
    private enum CodingKeys: String, CodingKey {
        case id, name, announcerName, eloRating, matchesPlayed, matchesWon
        case gamesPlayed, gamesWon, createdAt, competitionId, profileImageData, cutoutImageData
    }

    // Custom decoder for backwards compatibility
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        // Required fields
        id = try container.decode(UUID.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        eloRating = try container.decode(Double.self, forKey: .eloRating)
        matchesPlayed = try container.decode(Int.self, forKey: .matchesPlayed)
        matchesWon = try container.decode(Int.self, forKey: .matchesWon)
        gamesPlayed = try container.decode(Int.self, forKey: .gamesPlayed)
        gamesWon = try container.decode(Int.self, forKey: .gamesWon)
        createdAt = try container.decode(Date.self, forKey: .createdAt)
        competitionId = try container.decode(UUID.self, forKey: .competitionId)

        // Optional fields with defaults
        // For backwards compatibility: if announcerName is missing, use name
        announcerName = try container.decodeIfPresent(String.self, forKey: .announcerName) ?? name

        // Optional fields
        profileImageData = try container.decodeIfPresent(Data.self, forKey: .profileImageData)
        cutoutImageData = try container.decodeIfPresent(Data.self, forKey: .cutoutImageData)
    }

    // Standard encoder
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(announcerName, forKey: .announcerName)
        try container.encode(eloRating, forKey: .eloRating)
        try container.encode(matchesPlayed, forKey: .matchesPlayed)
        try container.encode(matchesWon, forKey: .matchesWon)
        try container.encode(gamesPlayed, forKey: .gamesPlayed)
        try container.encode(gamesWon, forKey: .gamesWon)
        try container.encode(createdAt, forKey: .createdAt)
        try container.encode(competitionId, forKey: .competitionId)

        // Only encode profileImageData if it exists
        if let profileImageData = profileImageData {
            try container.encode(profileImageData, forKey: .profileImageData)
        }

        // Only encode cutoutImageData if it exists
        if let cutoutImageData = cutoutImageData {
            try container.encode(cutoutImageData, forKey: .cutoutImageData)
        }
    }
}

// MARK: - Custom Codable Implementation for Match Storage
extension Player {
    /// Custom coding keys die profileImageData uitsluit voor Match records
    private enum MatchCodingKeys: String, CodingKey {
        case id, name, announcerName, eloRating, matchesPlayed, matchesWon
        case gamesPlayed, gamesWon, createdAt, competitionId
        // profileImageData wordt bewust weggelaten om CloudKit record size te beperken
    }

    /// Encodeert Player zonder profileImageData voor gebruik in Match records
    func encodeForMatch() throws -> Data {
        let encoder = JSONEncoder()
        var copy = self
        copy.profileImageData = nil // Verwijder profile image data om record size te beperken
        copy.cutoutImageData = nil // Verwijder cutout image data om record size te beperken
        return try encoder.encode(copy)
    }
}

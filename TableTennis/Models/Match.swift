import Foundation
import CloudKit

/// Type wedstrijd: enkel, dubbel of mix & match
enum MatchType: String, CaseIterable, Codable {
    case singles = "singles"
    case doubles = "doubles"
    case mixMatch = "mixMatch"

    var displayName: String {
        switch self {
        case .singles: return "Singles"
        case .doubles: return "Doubles"
        case .mixMatch: return "Mix & Match Doubles"
        }
    }
}

/// Status van een wedstrijd
enum MatchStatus: String, Codable {
    case scheduled = "scheduled"
    case inProgress = "inProgress"
    case completed = "completed"
    case cancelled = "cancelled"
    
    var displayName: String {
        switch self {
        case .scheduled: return "Scheduled"
        case .inProgress: return "In Progress"
        case .completed: return "Completed"
        case .cancelled: return "Cancelled"
        }
    }
}

/// Model voor een tafeltennis wedstrijd
struct Match: Identifiable, Codable {
    let id: UUID
    var type: MatchType
    var status: MatchStatus
    var bestOfGames: Int // Aantal games (oneven getal: 1, 3, 5, etc.)
    var createdAt: Date
    var completedAt: Date?
    var competitionId: UUID // Referentie naar de competitie

    // Team 1 spelers
    var team1Player1: Player
    var team1Player2: Player? // Alleen voor dubbels

    // Team 2 spelers
    var team2Player1: Player
    var team2Player2: Player? // Alleen voor dubbels

    // Mix & Match spelers (4 spelers voor rotatie dubbel)
    var mixMatchPlayer3: Player? // Alleen voor mix & match
    var mixMatchPlayer4: Player? // Alleen voor mix & match

    // Games in deze wedstrijd
    var games: [Game] = []

    // ELO rating veranderingen per speler (opgeslagen na match completion)
    var eloChanges: [UUID: Double] = [:] // Player ID -> ELO change

    init(type: MatchType,
         competitionId: UUID,
         bestOfGames: Int = 3,
         team1Player1: Player,
         team1Player2: Player? = nil,
         team2Player1: Player,
         team2Player2: Player? = nil,
         mixMatchPlayer3: Player? = nil,
         mixMatchPlayer4: Player? = nil) {
        self.id = UUID()
        self.type = type
        self.status = .scheduled
        self.bestOfGames = bestOfGames
        self.competitionId = competitionId
        self.team1Player1 = team1Player1
        self.team1Player2 = team1Player2
        self.team2Player1 = team2Player1
        self.team2Player2 = team2Player2
        self.mixMatchPlayer3 = mixMatchPlayer3
        self.mixMatchPlayer4 = mixMatchPlayer4
        self.createdAt = Date()
    }

    /// Convenience initializer voor Mix & Match wedstrijden
    static func createMixMatch(players: [Player], competitionId: UUID) -> Match {
        guard players.count == 4 else {
            fatalError("Mix & Match vereist exact 4 spelers")
        }

        return Match(
            type: .mixMatch,
            competitionId: competitionId,
            bestOfGames: 3, // Altijd 3 games voor Mix & Match
            team1Player1: players[0],
            team1Player2: nil,
            team2Player1: players[1],
            team2Player2: nil,
            mixMatchPlayer3: players[2],
            mixMatchPlayer4: players[3]
        )
    }
    
    /// Berekent het gecombineerde ELO van team 1
    var team1EloRating: Double {
        if type == .mixMatch {
            // Voor Mix & Match wordt ELO per game berekend
            return 0
        }
        let player1Elo = team1Player1.eloRating
        let player2Elo = team1Player2?.eloRating ?? 0
        return type == .doubles ? (player1Elo + player2Elo) / 2 : player1Elo
    }

    /// Berekent het gecombineerde ELO van team 2
    var team2EloRating: Double {
        if type == .mixMatch {
            // Voor Mix & Match wordt ELO per game berekend
            return 0
        }
        let player1Elo = team2Player1.eloRating
        let player2Elo = team2Player2?.eloRating ?? 0
        return type == .doubles ? (player1Elo + player2Elo) / 2 : player1Elo
    }
    
    /// Geeft alle spelers in de wedstrijd terug
    var allPlayers: [Player] {
        if type == .mixMatch {
            var players = [team1Player1, team2Player1]
            if let mixMatchPlayer3 = mixMatchPlayer3 { players.append(mixMatchPlayer3) }
            if let mixMatchPlayer4 = mixMatchPlayer4 { players.append(mixMatchPlayer4) }
            return players
        } else {
            var players = [team1Player1, team2Player1]
            if let team1Player2 = team1Player2 { players.append(team1Player2) }
            if let team2Player2 = team2Player2 { players.append(team2Player2) }
            return players
        }
    }

    /// Geeft alle 4 spelers voor Mix & Match terug
    var mixMatchPlayers: [Player] {
        guard type == .mixMatch else { return [] }
        var players = [team1Player1, team2Player1]
        if let mixMatchPlayer3 = mixMatchPlayer3 { players.append(mixMatchPlayer3) }
        if let mixMatchPlayer4 = mixMatchPlayer4 { players.append(mixMatchPlayer4) }
        return players
    }
    
    /// Berekent hoeveel games team 1 heeft gewonnen
    var team1GamesWon: Int {
        return games.filter { $0.winner == .team1 }.count
    }
    
    /// Berekent hoeveel games team 2 heeft gewonnen
    var team2GamesWon: Int {
        return games.filter { $0.winner == .team2 }.count
    }
    
    /// Bepaalt de winnaar van de wedstrijd
    var winner: Team? {
        if type == .mixMatch {
            // Voor Mix & Match: bepaal winnaar op basis van wie de meeste games heeft gewonnen
            guard games.count == 3 && games.allSatisfy({ $0.isCompleted }) else { return nil }

            // Tel games gewonnen per speler
            let playerWins = mixMatchPlayers.map { player in
                let gamesWon = games.filter { game in
                    guard let gameWinner = game.winner else { return false }
                    if gameWinner == .team1 {
                        return game.mixMatchTeam1Players?.contains(where: { $0.id == player.id }) ?? false
                    } else {
                        return game.mixMatchTeam2Players?.contains(where: { $0.id == player.id }) ?? false
                    }
                }.count
                return (player: player, wins: gamesWon)
            }

            let maxWins = playerWins.map { $0.wins }.max() ?? 0
            let winners = playerWins.filter { $0.wins == maxWins }

            // Als er één duidelijke winnaar is, return team1 (voor compatibiliteit)
            // De echte winnaar wordt bepaald in de UI via mixMatchWinnerText
            return winners.count == 1 ? .team1 : nil
        }
        let gamesNeededToWin = (bestOfGames / 2) + 1
        if team1GamesWon >= gamesNeededToWin { return .team1 }
        if team2GamesWon >= gamesNeededToWin { return .team2 }
        return nil
    }

    /// Controleert of de wedstrijd is afgelopen
    var isCompleted: Bool {
        if type == .mixMatch {
            return games.count == 3 && games.allSatisfy { $0.isCompleted }
        }
        return winner != nil
    }

    /// Bepaalt de winnaar(s) van een Mix & Match wedstrijd
    var mixMatchWinners: [Player] {
        guard type == .mixMatch && isCompleted else {
            print("🏆 DEBUG: Not Mix & Match or not completed")
            return []
        }

        print("🏆 DEBUG: Calculating Mix & Match winners...")
        print("🏆 DEBUG: Total games: \(games.count)")

        let playerWins = mixMatchPlayers.map { player in
            print("🏆 DEBUG: Checking player: \(player.name)")

            let gamesWon = games.filter { game in
                guard let gameWinner = game.winner else {
                    print("🏆 DEBUG: Game \(game.gameNumber) has no winner")
                    return false
                }

                print("🏆 DEBUG: Game \(game.gameNumber) winner: \(gameWinner)")
                print("🏆 DEBUG: Game \(game.gameNumber) Team1 players: \(game.mixMatchTeam1Players?.map { $0.name } ?? [])")
                print("🏆 DEBUG: Game \(game.gameNumber) Team2 players: \(game.mixMatchTeam2Players?.map { $0.name } ?? [])")

                if gameWinner == .team1 {
                    let isInTeam1 = game.mixMatchTeam1Players?.contains(where: { $0.id == player.id }) ?? false
                    print("🏆 DEBUG: Player \(player.name) in Team1 for game \(game.gameNumber): \(isInTeam1)")
                    return isInTeam1
                } else {
                    let isInTeam2 = game.mixMatchTeam2Players?.contains(where: { $0.id == player.id }) ?? false
                    print("🏆 DEBUG: Player \(player.name) in Team2 for game \(game.gameNumber): \(isInTeam2)")
                    return isInTeam2
                }
            }.count

            print("🏆 DEBUG: Player \(player.name) won \(gamesWon) games")
            return (player: player, wins: gamesWon)
        }

        let maxWins = playerWins.map { $0.wins }.max() ?? 0
        print("🏆 DEBUG: Max wins: \(maxWins)")

        let winners = playerWins.filter { $0.wins == maxWins }.map { $0.player }
        print("🏆 DEBUG: Winners: \(winners.map { $0.name })")

        return winners
    }
    
    /// Geeft een leesbare beschrijving van de wedstrijd
    var description: String {
        if type == .mixMatch {
            let playerNames = allPlayers.map { $0.name }.joined(separator: ", ")
            return playerNames
        }

        let team1Name = type == .doubles ?
            "\(team1Player1.name) & \(team1Player2?.name ?? "")" :
            team1Player1.name
        let team2Name = type == .doubles ?
            "\(team2Player1.name) & \(team2Player2?.name ?? "")" :
            team2Player1.name
        return "\(team1Name) vs \(team2Name)"
    }

    /// Genereert willekeurige team combinaties voor Mix & Match games
    /// Zorgt ervoor dat iedereen met iedereen speelt, maar in willekeurige volgorde en posities
    func getMixMatchTeamCombinations() -> [(team1: [Player], team2: [Player])] {
        guard type == .mixMatch,
              let player3 = mixMatchPlayer3,
              let player4 = mixMatchPlayer4 else {
            return []
        }

        let players = [team1Player1, team2Player1, player3, player4]

        // Alle mogelijke combinaties voor 4 spelers (A, B, C, D):
        // Combinatie 1: A+B vs C+D
        // Combinatie 2: A+C vs B+D
        // Combinatie 3: A+D vs B+C
        var allCombinations = [
            (team1: [players[0], players[1]], team2: [players[2], players[3]]), // A+B vs C+D
            (team1: [players[0], players[2]], team2: [players[1], players[3]]), // A+C vs B+D
            (team1: [players[0], players[3]], team2: [players[1], players[2]])  // A+D vs B+C
        ]

        // Shuffle de volgorde van de combinaties
        allCombinations.shuffle()

        // Shuffle ook de posities binnen elk team en tussen teams
        var randomizedCombinations: [(team1: [Player], team2: [Player])] = []

        for combination in allCombinations {
            var team1Players = combination.team1
            var team2Players = combination.team2

            // Shuffle spelers binnen elk team
            team1Players.shuffle()
            team2Players.shuffle()

            // Willekeurig bepalen welk team "team1" en welk team "team2" wordt
            if Bool.random() {
                randomizedCombinations.append((team1: team1Players, team2: team2Players))
            } else {
                randomizedCombinations.append((team1: team2Players, team2: team1Players))
            }
        }

        return randomizedCombinations
    }

    /// Geeft de team namen voor een specifieke Mix & Match game
    func getMixMatchGameDescription(gameIndex: Int) -> String {
        let combinations = getMixMatchTeamCombinations()
        guard gameIndex < combinations.count else { return "" }

        let combination = combinations[gameIndex]
        let team1Names = combination.team1.map { $0.name }.joined(separator: " & ")
        let team2Names = combination.team2.map { $0.name }.joined(separator: " & ")

        return "\(team1Names) vs \(team2Names)"
    }

    /// Genereert automatisch de 3 games voor Mix & Match met willekeurige team indelingen
    mutating func generateMixMatchGames() {
        guard type == .mixMatch else { return }

        let combinations = getMixMatchTeamCombinations()
        games = []

        for (index, combination) in combinations.enumerated() {
            let game = Game(
                gameNumber: index + 1,
                mixMatchTeam1Players: combination.team1,
                mixMatchTeam2Players: combination.team2
            )
            games.append(game)
        }
    }


}

// MARK: - CloudKit Support
extension Match {
    static let recordType = "Match"

    /// Initializer vanuit CloudKit CKRecord
    init?(from record: CKRecord) {
        guard let typeString = record["type"] as? String,
              let type = MatchType(rawValue: typeString),
              let statusString = record["status"] as? String,
              let status = MatchStatus(rawValue: statusString),
              let bestOfGames = record["bestOfGames"] as? Int,
              let createdAt = record["createdAt"] as? Date,
              let competitionIdString = record["competitionId"] as? String,
              let competitionId = UUID(uuidString: competitionIdString),
              let team1Player1Data = record["team1Player1"] as? Data,
              let team2Player1Data = record["team2Player1"] as? Data,
              let team1Player1 = try? JSONDecoder().decode(Player.self, from: team1Player1Data),
              let team2Player1 = try? JSONDecoder().decode(Player.self, from: team2Player1Data),
              let idString = record.recordID.recordName.components(separatedBy: "_").last,
              let id = UUID(uuidString: idString) else {
            return nil
        }

        self.id = id
        self.type = type
        self.status = status
        self.bestOfGames = bestOfGames
        self.createdAt = createdAt
        self.competitionId = competitionId
        self.completedAt = record["completedAt"] as? Date
        self.team1Player1 = team1Player1
        self.team2Player1 = team2Player1

        // Optional players
        if let team1Player2Data = record["team1Player2"] as? Data,
           let team1Player2 = try? JSONDecoder().decode(Player.self, from: team1Player2Data) {
            self.team1Player2 = team1Player2
        }

        if let team2Player2Data = record["team2Player2"] as? Data,
           let team2Player2 = try? JSONDecoder().decode(Player.self, from: team2Player2Data) {
            self.team2Player2 = team2Player2
        }

        if let mixMatchPlayer3Data = record["mixMatchPlayer3"] as? Data,
           let mixMatchPlayer3 = try? JSONDecoder().decode(Player.self, from: mixMatchPlayer3Data) {
            self.mixMatchPlayer3 = mixMatchPlayer3
        }

        if let mixMatchPlayer4Data = record["mixMatchPlayer4"] as? Data,
           let mixMatchPlayer4 = try? JSONDecoder().decode(Player.self, from: mixMatchPlayer4Data) {
            self.mixMatchPlayer4 = mixMatchPlayer4
        }

        // Games
        if let gamesData = record["games"] as? Data {
            print("🎮 DEBUG: Found games data in CloudKit record, size: \(gamesData.count) bytes")
            do {
                let games = try JSONDecoder().decode([Game].self, from: gamesData)
                print("🎮 DEBUG: Successfully decoded \(games.count) games from CloudKit")
                for (index, game) in games.enumerated() {
                    print("🎮 DEBUG: Game \(index + 1): gameNumber=\(game.gameNumber), team1Score=\(game.team1Score), team2Score=\(game.team2Score), isCompleted=\(game.isCompleted)")
                }
                self.games = games
            } catch {
                print("❌ DEBUG: Failed to decode games from CloudKit data: \(error)")
                print("❌ DEBUG: Error details: \(error.localizedDescription)")
                self.games = []
            }
        } else {
            print("⚠️ DEBUG: No games data found in CloudKit record")
            self.games = []
        }

        // ELO Changes
        if let eloChangesData = record["eloChanges"] as? Data,
           let eloChanges = try? JSONDecoder().decode([String: Double].self, from: eloChangesData) {
            // Convert String keys back to UUID
            self.eloChanges = Dictionary(uniqueKeysWithValues:
                eloChanges.compactMap { key, value in
                    guard let uuid = UUID(uuidString: key) else { return nil }
                    return (uuid, value)
                }
            )
        } else {
            self.eloChanges = [:]
        }
    }

    /// Converteert naar CloudKit CKRecord
    func toCKRecord() -> CKRecord {
        let recordID = CKRecord.ID(recordName: "Match_\(id.uuidString)")
        let record = CKRecord(recordType: Match.recordType, recordID: recordID)

        record["type"] = type.rawValue
        record["status"] = status.rawValue
        record["bestOfGames"] = bestOfGames
        record["createdAt"] = createdAt
        record["competitionId"] = competitionId.uuidString
        record["completedAt"] = completedAt

        // Encode players as Data (without profileImageData to reduce record size)
        if let team1Player1Data = try? team1Player1.encodeForMatch() {
            print("🎮 DEBUG: team1Player1 data size: \(team1Player1Data.count) bytes")
            record["team1Player1"] = team1Player1Data
        }
        if let team2Player1Data = try? team2Player1.encodeForMatch() {
            print("🎮 DEBUG: team2Player1 data size: \(team2Player1Data.count) bytes")
            record["team2Player1"] = team2Player1Data
        }
        if let team1Player2 = team1Player2,
           let team1Player2Data = try? team1Player2.encodeForMatch() {
            record["team1Player2"] = team1Player2Data
        }
        if let team2Player2 = team2Player2,
           let team2Player2Data = try? team2Player2.encodeForMatch() {
            record["team2Player2"] = team2Player2Data
        }
        if let mixMatchPlayer3 = mixMatchPlayer3,
           let mixMatchPlayer3Data = try? mixMatchPlayer3.encodeForMatch() {
            record["mixMatchPlayer3"] = mixMatchPlayer3Data
        }
        if let mixMatchPlayer4 = mixMatchPlayer4,
           let mixMatchPlayer4Data = try? mixMatchPlayer4.encodeForMatch() {
            record["mixMatchPlayer4"] = mixMatchPlayer4Data
        }

        // Encode games as Data
        print("🎮 DEBUG: Encoding \(games.count) games to CloudKit")
        for (index, game) in games.enumerated() {
            print("🎮 DEBUG: Game \(index + 1): gameNumber=\(game.gameNumber), team1Score=\(game.team1Score), team2Score=\(game.team2Score), isCompleted=\(game.isCompleted), scoreHistory=\(game.scoreHistory.count) points")

            // Debug Mix & Match player data size
            if let team1Players = game.mixMatchTeam1Players {
                print("🎮 DEBUG: Game \(index + 1) has \(team1Players.count) team1 players")
            }
            if let team2Players = game.mixMatchTeam2Players {
                print("🎮 DEBUG: Game \(index + 1) has \(team2Players.count) team2 players")
            }
        }
        if let gamesData = try? JSONEncoder().encode(games) {
            print("🎮 DEBUG: Successfully encoded games to \(gamesData.count) bytes")

            // Debug: Check individual game sizes
            for (index, game) in games.enumerated() {
                if let singleGameData = try? JSONEncoder().encode(game) {
                    print("🎮 DEBUG: Game \(index + 1) size: \(singleGameData.count) bytes")
                }
            }

            record["games"] = gamesData
        } else {
            print("❌ DEBUG: Failed to encode games to CloudKit")
        }

        // Encode ELO changes as Data (convert UUID keys to String for JSON compatibility)
        let eloChangesStringKeys = Dictionary(uniqueKeysWithValues:
            eloChanges.map { key, value in (key.uuidString, value) }
        )
        if let eloChangesData = try? JSONEncoder().encode(eloChangesStringKeys) {
            print("🎮 DEBUG: eloChanges data size: \(eloChangesData.count) bytes (\(eloChanges.count) entries)")
            record["eloChanges"] = eloChangesData
        }

        // Debug: Calculate total record size estimate
        var totalSize = 0
        for key in record.allKeys() {
            if let value = record[key] {
                if let data = value as? Data {
                    totalSize += data.count
                } else if let string = value as? String {
                    totalSize += string.utf8.count
                } else if let date = value as? Date {
                    totalSize += 8 // Approximate size of Date
                } else if let number = value as? NSNumber {
                    totalSize += 8 // Approximate size of numbers
                }
            }
        }
        print("🎮 DEBUG: Estimated total record size: \(totalSize) bytes")

        return record
    }

    /// Converteert naar CloudKit CKRecord en update een bestaand record
    func toCKRecord(existingRecord: CKRecord) -> CKRecord {
        // Update het bestaande record met nieuwe data
        existingRecord["type"] = type.rawValue
        existingRecord["status"] = status.rawValue
        existingRecord["bestOfGames"] = bestOfGames
        existingRecord["createdAt"] = createdAt
        existingRecord["competitionId"] = competitionId.uuidString
        existingRecord["completedAt"] = completedAt

        // Encode players as Data (without profileImageData to reduce record size)
        if let team1Player1Data = try? team1Player1.encodeForMatch() {
            existingRecord["team1Player1"] = team1Player1Data
        }
        if let team2Player1Data = try? team2Player1.encodeForMatch() {
            existingRecord["team2Player1"] = team2Player1Data
        }
        if let team1Player2 = team1Player2,
           let team1Player2Data = try? team1Player2.encodeForMatch() {
            existingRecord["team1Player2"] = team1Player2Data
        } else {
            existingRecord["team1Player2"] = nil
        }
        if let team2Player2 = team2Player2,
           let team2Player2Data = try? team2Player2.encodeForMatch() {
            existingRecord["team2Player2"] = team2Player2Data
        } else {
            existingRecord["team2Player2"] = nil
        }
        if let mixMatchPlayer3 = mixMatchPlayer3,
           let mixMatchPlayer3Data = try? mixMatchPlayer3.encodeForMatch() {
            existingRecord["mixMatchPlayer3"] = mixMatchPlayer3Data
        } else {
            existingRecord["mixMatchPlayer3"] = nil
        }
        if let mixMatchPlayer4 = mixMatchPlayer4,
           let mixMatchPlayer4Data = try? mixMatchPlayer4.encodeForMatch() {
            existingRecord["mixMatchPlayer4"] = mixMatchPlayer4Data
        } else {
            existingRecord["mixMatchPlayer4"] = nil
        }

        // Encode games as Data
        print("🎮 DEBUG: Updating existing record with \(games.count) games")
        for (index, game) in games.enumerated() {
            print("🎮 DEBUG: Game \(index + 1): gameNumber=\(game.gameNumber), team1Score=\(game.team1Score), team2Score=\(game.team2Score), isCompleted=\(game.isCompleted)")
        }
        if let gamesData = try? JSONEncoder().encode(games) {
            print("🎮 DEBUG: Successfully encoded games to \(gamesData.count) bytes for update")
            existingRecord["games"] = gamesData
        } else {
            print("❌ DEBUG: Failed to encode games for update")
        }

        // Encode ELO changes as Data (convert UUID keys to String for JSON compatibility)
        let eloChangesStringKeys = Dictionary(uniqueKeysWithValues:
            eloChanges.map { key, value in (key.uuidString, value) }
        )
        if let eloChangesData = try? JSONEncoder().encode(eloChangesStringKeys) {
            existingRecord["eloChanges"] = eloChangesData
        }

        return existingRecord
    }
}

import SwiftUI
import Charts
import Vision
import CoreImage
import CoreImage.CIFilterBuiltins

struct MatchDetailView: View {
    @EnvironmentObject var dataManager: DataManager
    let match: Match
    @State private var showingLiveMatch = false
    @State private var showingEditMatch = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Match Header
                MatchDetailHeaderView(match: match, dataManager: dataManager)
                
                // Match Status and Actions
                MatchActionsView(
                    match: match,
                    onPlayLive: { showingLiveMatch = true },
                    onEdit: { showingEditMatch = true }
                )
                
                // Games Overview
                if !match.games.isEmpty {
                    GamesOverviewView(match: match)
                }
                
                // Player Statistics for this match
                if match.status == .completed {
                    MatchPlayerStatsView(match: match)
                }
                
                // Match Timeline
                MatchTimelineView(match: match)
            }
            .padding()
        }
        .navigationTitle("Match Details")
        .navigationBarTitleDisplayMode(.inline)
        .fullScreenCover(isPresented: $showingLiveMatch) {
            NavigationView {
                LiveMatchView(match: match)
            }
        }
        .sheet(isPresented: $showingEditMatch) {
            EditMatchView(match: match)
        }
    }
}

struct MatchDetailHeaderView: View {
    let match: Match
    let dataManager: DataManager

    var body: some View {
        ZStack(alignment: .bottom) {
            // Background player images for singles matches only - positioned in corners
            if match.type == .singles {
                GeometryReader { geometry in
                    ZStack {
                        // Left player background image (cut-out) - bottom left corner
                        if let currentPlayer = dataManager.players.first(where: { $0.id == match.team1Player1.id }),
                           currentPlayer.profileImageData != nil {
                            CutoutPlayerImage(
                                player: currentPlayer,
                                size: geometry.size.width * 0.25, // 25% van de breedte
                                showShineEffect: match.status == .completed && match.winner == .team1
                            )
                            .opacity(0.25)
                            .allowsHitTesting(false)
                            .position(
                                x: geometry.size.width * 0.125, // Gecentreerd in linker kwart
                                y: geometry.size.height - (geometry.size.width * 0.25 * 0.55) // Onderaan gepositioneerd
                            )
                        }

                        // Right player background image (cut-out) - bottom right corner
                        if let currentPlayer = dataManager.players.first(where: { $0.id == match.team2Player1.id }),
                           currentPlayer.profileImageData != nil {
                            CutoutPlayerImage(
                                player: currentPlayer,
                                size: geometry.size.width * 0.25, // 25% van de breedte
                                showShineEffect: match.status == .completed && match.winner == .team2
                            )
                            .opacity(0.25)
                            .allowsHitTesting(false)
                            .position(
                                x: geometry.size.width * 0.875, // Gecentreerd in rechter kwart
                                y: geometry.size.height - (geometry.size.width * 0.25 * 0.55) // Onderaan gepositioneerd
                            )
                        }
                    }
                }
            }

            // Foreground content
            VStack(spacing: 16) {
                // Match Type Badge
                HStack {
                    Label(match.type.displayName, systemImage: match.type == .singles ? "person" : "person.2")
                        .font(.subheadline)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Color.blue.opacity(0.2))
                        .cornerRadius(8)

                    Spacer()

                    Text("Best of \(match.bestOfGames)")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                if match.type == .mixMatch {
                    // Mix & Match: Toon alle 4 spelers met hun game wins
                    mixMatchPlayersView
                } else {
                    // Reguliere wedstrijden: Toon teams
                    regularMatchTeamsView
                }

                // Winner Badge
                if match.status == .completed {
                    if match.type == .mixMatch {
                        // For Mix & Match: show player(s) with most games
                        if let winnerText = mixMatchWinnerText {
                            Text(winnerText)
                                .font(.headline)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 20)
                                .padding(.vertical, 8)
                                .background(Color.green)
                                .cornerRadius(20)
                        }
                    } else if let winner = match.winner {
                        // For regular matches: show winner
                        Text(match.type == .singles ?
                             (winner == .team1 ? "\(match.team1Player1.name) Wins!" : "\(match.team2Player1.name) Wins!") :
                             "\(winner.displayName) Wins!")
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 20)
                            .padding(.vertical, 8)
                            .background(Color.green)
                            .cornerRadius(20)
                    }
                }
            }
            .padding()
        }
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    private var mixMatchPlayersView: some View {
        VStack(spacing: 12) {
            Text("Players")
                .font(.caption)
                .foregroundColor(.secondary)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                ForEach(match.mixMatchPlayers) { player in
                    VStack(spacing: 6) {
                        Text(player.name)
                            .font(.headline)
                            .fontWeight(.bold)
                            .multilineTextAlignment(.center)

                        if match.status == .completed || match.status == .inProgress {
                            VStack(spacing: 2) {
                                Text("\(gamesWonByPlayer(player))")
                                    .font(.system(size: 24, weight: .bold, design: .rounded))
                                    .foregroundColor(.primary)

                                Text("games won")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    .padding(.vertical, 8)
                    .frame(maxWidth: .infinity)
                    .background(Color(.systemBackground))
                    .cornerRadius(8)
                }
            }
        }
    }

    private var regularMatchTeamsView: some View {
        HStack(spacing: 20) {
            // Team 1
            VStack(spacing: 8) {
                Text(match.type == .singles ? match.team1Player1.name : "Team 1")
                    .font(.caption)
                    .foregroundColor(.secondary)

                VStack(spacing: 4) {
                    Text(match.team1Player1.name)
                        .font(.headline)
                        .fontWeight(.bold)

                    if let team1Player2 = match.team1Player2 {
                        Text(team1Player2.name)
                            .font(.subheadline)
                    }
                }

                if match.status == .completed || match.status == .inProgress {
                    Text("\(match.team1GamesWon)")
                        .font(.system(size: 36, weight: .bold, design: .rounded))
                        .foregroundColor(.blue)
                }
            }

            Text("vs")
                .font(.title2)
                .foregroundColor(.secondary)

            // Team 2
            VStack(spacing: 8) {
                Text(match.type == .singles ? match.team2Player1.name : "Team 2")
                    .font(.caption)
                    .foregroundColor(.secondary)

                VStack(spacing: 4) {
                    Text(match.team2Player1.name)
                        .font(.headline)
                        .fontWeight(.bold)

                    if let team2Player2 = match.team2Player2 {
                        Text(team2Player2.name)
                            .font(.subheadline)
                    }
                }

                if match.status == .completed || match.status == .inProgress {
                    Text("\(match.team2GamesWon)")
                        .font(.system(size: 36, weight: .bold, design: .rounded))
                        .foregroundColor(.red)
                }
            }
        }
    }

    private func gamesWonByPlayer(_ player: Player) -> Int {
        return match.games.filter { game in
            guard let winner = game.winner else { return false }

            // Check of de speler in het winnende team zat voor deze game
            if winner == .team1 {
                return game.mixMatchTeam1Players?.contains(where: { $0.id == player.id }) ?? false
            } else {
                return game.mixMatchTeam2Players?.contains(where: { $0.id == player.id }) ?? false
            }
        }.count
    }

    private var mixMatchWinnerText: String? {
        guard match.type == .mixMatch && match.status == .completed else { return nil }

        let winners = match.mixMatchWinners
        guard !winners.isEmpty else { return nil }

        let maxWins = winners.compactMap { player in
            gamesWonByPlayer(player)
        }.max() ?? 0

        if winners.count == 1 {
            return "\(winners[0].name) Wint! (\(maxWins) games)"
        } else if winners.count > 1 {
            let winnerNames = winners.map { $0.name }.joined(separator: " & ")
            return "\(winnerNames) Delen de Winst! (\(maxWins) games elk)"
        }

        return nil
    }
}

struct MatchActionsView: View {
    let match: Match
    let onPlayLive: () -> Void
    let onEdit: () -> Void
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                // Status Badge
                Label(match.status.displayName, systemImage: statusIcon)
                    .font(.subheadline)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(statusColor.opacity(0.2))
                    .foregroundColor(statusColor)
                    .cornerRadius(8)
                
                Spacer()
                
                // Action Buttons
                HStack(spacing: 12) {
                    if match.status == .scheduled || match.status == .inProgress {
                        Button("Live Spelen") {
                            onPlayLive()
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    
                    Button("Bewerken") {
                        onEdit()
                    }
                    .buttonStyle(.bordered)
                }
            }
        }
    }
    
    private var statusIcon: String {
        switch match.status {
        case .scheduled: return "calendar"
        case .inProgress: return "play.circle"
        case .completed: return "checkmark.circle"
        case .cancelled: return "xmark.circle"
        }
    }
    
    private var statusColor: Color {
        switch match.status {
        case .scheduled: return .blue
        case .inProgress: return .orange
        case .completed: return .green
        case .cancelled: return .red
        }
    }
}

struct GamesOverviewView: View {
    let match: Match
    @State private var selectedGame: Game?

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Games Overview")
                .font(.headline)

            // Links uitgelijnde layout met maximaal 3 kolommen
            LazyVGrid(columns: [
                GridItem(.flexible(), alignment: .leading),
                GridItem(.flexible(), alignment: .leading),
                GridItem(.flexible(), alignment: .leading)
            ], alignment: .leading, spacing: 12) {
                ForEach(match.games) { game in
                    GameSummaryCard(game: game, match: match, selectedGame: $selectedGame)
                }
            }

            // Inline score chart voor geselecteerde game
            if let selectedGame = selectedGame {
                GameScoreInlineView(game: selectedGame, match: match)
                    .transition(.opacity.combined(with: .move(edge: .top)))
                    .animation(.easeInOut(duration: 0.3), value: selectedGame.id)
            }
        }
    }
}

struct GameSummaryCard: View {
    let game: Game
    let match: Match
    @Binding var selectedGame: Game?

    // Computed property voor expected scores
    private var expectedScoreText: String? {
        guard let team1Expected = game.team1ExpectedScore,
              let team2Expected = game.team2ExpectedScore else {
            return nil
        }

        let team1ExpectedPoints: Int
        let team2ExpectedPoints: Int

        if abs(team1Expected - team2Expected) < 0.01 {
            // Gelijkspel: beide teams krijgen 10 punten
            team1ExpectedPoints = 10
            team2ExpectedPoints = 10
        } else if team1Expected > team2Expected {
            // Team 1 wint: krijgt 11 punten, team 2 naar verhouding
            team1ExpectedPoints = 11
            team2ExpectedPoints = Int(team2Expected / team1Expected * 11.0)
        } else {
            // Team 2 wint: krijgt 11 punten, team 1 naar verhouding
            team2ExpectedPoints = 11
            team1ExpectedPoints = Int(team1Expected / team2Expected * 11.0)
        }

        return "(\(team1ExpectedPoints) - \(team2ExpectedPoints))"
    }

    var body: some View {
        Button(action: {
            if game.isCompleted && !game.scoreHistory.isEmpty {
                selectedGame = selectedGame?.id == game.id ? nil : game
            }
        }) {
            VStack(alignment: .leading, spacing: 8) {
                HStack(alignment: .center) {
                    Text("Game \(game.gameNumber)")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)

                    Spacer()

                    HStack(spacing: 4) {
                        if game.isCompleted, let winner = game.winner {
                            Circle()
                                .fill(winner == .team1 ? Color.blue : Color.red)
                                .frame(width: 10, height: 10)
                        } else {
                            Circle()
                                .fill(Color.orange)
                                .frame(width: 10, height: 10)
                        }

                        // Chart icon voor voltooide games met score history
                        if game.isCompleted && !game.scoreHistory.isEmpty {
                            Image(systemName: selectedGame?.id == game.id ? "chart.line.downtrend.xyaxis" : "chart.line.uptrend.xyaxis")
                                .font(.caption2)
                                .foregroundColor(.blue)
                        }
                    }
                }

                // Team samenstellingen voor Mix & Match
                if match.type == .mixMatch {
                    VStack(alignment: .leading, spacing: 4) {
                        if let team1Players = game.mixMatchTeam1Players, team1Players.count >= 2 {
                            Text("\(team1Players[0].name) & \(team1Players[1].name)")
                                .font(.caption2)
                                .foregroundColor(.blue)
                                .lineLimit(1)
                        }

                        Text("vs")
                            .font(.caption2)
                            .foregroundColor(.secondary)

                        if let team2Players = game.mixMatchTeam2Players, team2Players.count >= 2 {
                            Text("\(team2Players[0].name) & \(team2Players[1].name)")
                                .font(.caption2)
                                .foregroundColor(.red)
                                .lineLimit(1)
                        }
                    }
                }

                HStack {
                    VStack(alignment: .leading, spacing: 2) {
                        Text("\(game.team1Score) - \(game.team2Score)")
                            .font(.title3)
                            .fontWeight(.bold)
                            .multilineTextAlignment(.leading)
                            .foregroundColor(.primary)

                        // Toon expected scores als beschikbaar
                        if let expectedText = expectedScoreText {
                            Text(expectedText)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }

                    Spacer()
                }
            }
            .padding(12)
            .background(Color(.systemGray6))
            .cornerRadius(8)
            .frame(maxWidth: .infinity, alignment: .leading)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(
                        selectedGame?.id == game.id ? Color.blue : (game.isCompleted && !game.scoreHistory.isEmpty ? Color.blue.opacity(0.3) : Color.clear),
                        lineWidth: selectedGame?.id == game.id ? 2 : 1
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(!game.isCompleted || game.scoreHistory.isEmpty)
    }
}

struct GameScoreInlineView: View {
    let game: Game
    let match: Match

    private var chartData: [ScoreDataPoint] {
        var data: [ScoreDataPoint] = []
        var team1Score = 0
        var team2Score = 0

        // Start punt (0-0)
        data.append(ScoreDataPoint(pointNumber: 0, team1Score: 0, team2Score: 0))

        // Voeg elk punt toe
        for (index, scorePoint) in game.scoreHistory.enumerated() {
            if scorePoint.team == .team1 {
                team1Score += 1
            } else {
                team2Score += 1
            }

            data.append(ScoreDataPoint(
                pointNumber: index + 1,
                team1Score: team1Score,
                team2Score: team2Score
            ))
        }

        return data
    }

    private var team1Name: String {
        if match.type == .mixMatch {
            if let players = game.mixMatchTeam1Players, players.count >= 2 {
                return "\(players[0].name) & \(players[1].name)"
            }
            return "Team 1"
        } else if match.type == .singles {
            return match.team1Player1.name
        } else {
            let player2Name = match.team1Player2?.name ?? ""
            return "\(match.team1Player1.name) & \(player2Name)"
        }
    }

    private var team2Name: String {
        if match.type == .mixMatch {
            if let players = game.mixMatchTeam2Players, players.count >= 2 {
                return "\(players[0].name) & \(players[1].name)"
            }
            return "Team 2"
        } else if match.type == .singles {
            return match.team2Player1.name
        } else {
            let player2Name = match.team2Player2?.name ?? ""
            return "\(match.team2Player1.name) & \(player2Name)"
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            HStack {
                Text("Game \(game.gameNumber) Score Progress")
                    .font(.headline)
                    .foregroundColor(.primary)

                Spacer()

                HStack(spacing: 16) {
                    Text("\(game.team1Score)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)

                    Text("-")
                        .font(.title2)
                        .foregroundColor(.secondary)

                    Text("\(game.team2Score)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.red)
                }
            }

            // Chart
            if !chartData.isEmpty {
                Chart(chartData) { dataPoint in
                    // Team 1 lijn (blauw)
                    LineMark(
                        x: .value("Totaal Punten", dataPoint.pointNumber),
                        y: .value("Score", dataPoint.team1Score),
                        series: .value("Team", "Team 1")
                    )
                    .foregroundStyle(Color.blue)
                    .lineStyle(StrokeStyle(lineWidth: 3))
                    .interpolationMethod(.linear)

                    // Team 2 lijn (rood)
                    LineMark(
                        x: .value("Totaal Punten", dataPoint.pointNumber),
                        y: .value("Score", dataPoint.team2Score),
                        series: .value("Team", "Team 2")
                    )
                    .foregroundStyle(Color.red)
                    .lineStyle(StrokeStyle(lineWidth: 3))
                    .interpolationMethod(.linear)
                }
                .frame(height: 250)
                .chartXAxis {
                    AxisMarks(values: .automatic(desiredCount: 8)) { value in
                        AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5))
                            .foregroundStyle(.gray.opacity(0.3))
                        AxisTick()
                        AxisValueLabel() {
                            if let intValue = value.as(Int.self) {
                                Text("\(intValue)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
                .chartYAxis {
                    AxisMarks(values: .automatic(desiredCount: 8)) { value in
                        AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5))
                            .foregroundStyle(.gray.opacity(0.3))
                        AxisTick()
                        AxisValueLabel() {
                            if let intValue = value.as(Int.self) {
                                Text("\(intValue)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
                .chartXAxisLabel("Totaal Gespeelde Punten", alignment: .center)
                .chartYAxisLabel("Aantal Punten per Team", alignment: .center)
                .chartLegend(position: .bottom) {
                    HStack(spacing: 30) {
                        HStack(spacing: 8) {
                            Rectangle()
                                .fill(Color.blue)
                                .frame(width: 20, height: 3)
                            Text(team1Name)
                                .font(.caption)
                                .foregroundColor(Color.blue)
                                .fontWeight(.medium)
                        }

                        HStack(spacing: 8) {
                            Rectangle()
                                .fill(Color.red)
                                .frame(width: 20, height: 3)
                            Text(team2Name)
                                .font(.caption)
                                .foregroundColor(Color.red)
                                .fontWeight(.medium)
                        }
                    }
                }
            }

            // ELO Changes voor Mix & Match wedstrijden
            if match.type == .mixMatch && !game.eloChanges.isEmpty {
                VStack(alignment: .leading, spacing: 12) {
                    Text("ELO Changes")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)

                    VStack(spacing: 6) {
                        // Team 1 spelers
                        if let team1Players = game.mixMatchTeam1Players {
                            ForEach(team1Players) { player in
                                GameEloChangeRow(
                                    player: player,
                                    eloChange: game.eloChanges[player.id] ?? 0.0,
                                    teamColor: .blue
                                )
                            }
                        }

                        // Team 2 spelers
                        if let team2Players = game.mixMatchTeam2Players {
                            ForEach(team2Players) { player in
                                GameEloChangeRow(
                                    player: player,
                                    eloChange: game.eloChanges[player.id] ?? 0.0,
                                    teamColor: .red
                                )
                            }
                        }
                    }
                    .padding(12)
                    .background(Color(.systemBackground))
                    .cornerRadius(8)
                }
                .padding(.top, 12)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct GameEloChangeRow: View {
    let player: Player
    let eloChange: Double
    let teamColor: Color

    private var eloChangeText: String {
        if eloChange > 0 {
            return "+\(Int(eloChange.rounded()))"
        } else if eloChange < 0 {
            return "\(Int(eloChange.rounded()))"
        } else {
            return "0"
        }
    }

    private var eloChangeColor: Color {
        if eloChange > 0 {
            return .green
        } else if eloChange < 0 {
            return .red
        } else {
            return .secondary
        }
    }

    var body: some View {
        HStack(spacing: 12) {
            // Team indicator
            Circle()
                .fill(teamColor)
                .frame(width: 8, height: 8)

            // Player name
            Text(player.name)
                .font(.caption)
                .foregroundColor(.primary)
                .lineLimit(1)

            Spacer()

            // ELO change
            HStack(spacing: 4) {
                Image(systemName: eloChange >= 0 ? "arrow.up" : "arrow.down")
                    .font(.caption2)
                    .foregroundColor(eloChangeColor)

                Text(eloChangeText)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(eloChangeColor)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
    }
}

struct MatchPlayerStatsView: View {
    let match: Match
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Player Statistics")
                .font(.headline)
            
            VStack(spacing: 8) {
                ForEach(match.allPlayers) { player in
                    PlayerMatchStatsRow(player: player, match: match)
                }
            }
        }
    }
}

struct PlayerMatchStatsRow: View {
    let player: Player
    let match: Match

    var gamesWon: Int {
        return match.games.filter { game in
            guard let winner = game.winner else { return false }

            if match.type == .mixMatch {
                // Voor Mix & Match: controleer of speler daadwerkelijk in het winnende team speelde
                if winner == .team1 {
                    return game.mixMatchTeam1Players?.contains(where: { $0.id == player.id }) ?? false
                } else {
                    return game.mixMatchTeam2Players?.contains(where: { $0.id == player.id }) ?? false
                }
            } else if match.type == .singles {
                return (player.id == match.team1Player1.id && winner == .team1) ||
                       (player.id == match.team2Player1.id && winner == .team2)
            } else {
                // Voor reguliere doubles
                let isTeam1 = player.id == match.team1Player1.id || player.id == match.team1Player2?.id
                return (isTeam1 && winner == .team1) || (!isTeam1 && winner == .team2)
            }
        }.count
    }



    var totalPoints: Int {
        return match.games.reduce(0) { total, game in
            if match.type == .singles {
                if player.id == match.team1Player1.id {
                    return total + game.team1Score
                } else if player.id == match.team2Player1.id {
                    return total + game.team2Score
                }
            } else if match.type == .mixMatch {
                // Voor Mix & Match: controleer per game in welk team de speler speelde
                if let team1Players = game.mixMatchTeam1Players,
                   team1Players.contains(where: { $0.id == player.id }) {
                    return total + game.team1Score
                } else if let team2Players = game.mixMatchTeam2Players,
                          team2Players.contains(where: { $0.id == player.id }) {
                    return total + game.team2Score
                }
            } else {
                // Voor reguliere doubles
                let isTeam1 = player.id == match.team1Player1.id || player.id == match.team1Player2?.id
                return total + (isTeam1 ? game.team1Score : game.team2Score)
            }
            return total
        }
    }

    var eloChange: Double {
        guard match.status == .completed else { return 0.0 }

        // Gebruik ALTIJD opgeslagen ELO change uit de match
        // Dit zorgt voor consistentie tussen MatchDetailView en PlayerDetailView
        if let storedChange = match.eloChanges[player.id] {
            return storedChange
        }

        // Als er geen opgeslagen change is, return 0 (dit zou niet moeten gebeuren voor nieuwe matches)
        print("⚠️ Geen opgeslagen ELO change gevonden voor speler \(player.name) in match \(match.id)")
        return 0.0
    }
    
    var body: some View {
        HStack {
            Text(player.name)
                .font(.subheadline)
                .fontWeight(.medium)

            Spacer()

            HStack(spacing: 20) {
                VStack(alignment: .center, spacing: 2) {
                    Text("Won")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    Text("\(gamesWon)/\(match.games.count)")
                        .font(.caption)
                        .fontWeight(.medium)
                        .fixedSize()
                }
                .frame(minWidth: 40)

                VStack(alignment: .center, spacing: 2) {
                    Text("Points")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    Text("\(totalPoints)")
                        .font(.caption)
                        .fontWeight(.medium)
                        .fixedSize()
                }
                .frame(minWidth: 40)

                if match.status == .completed {
                    VStack(alignment: .center, spacing: 2) {
                        Text("ELO")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                        Text(eloChangeText)
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(eloChangeColor)
                            .fixedSize()
                    }
                    .frame(minWidth: 40)
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }

    private var eloChangeText: String {
        let change = eloChange
        if change > 0 {
            return "+\(Int(change.rounded()))"
        } else if change < 0 {
            return "\(Int(change.rounded()))"
        } else {
            return "0"
        }
    }

    private var eloChangeColor: Color {
        let change = eloChange
        if change > 0 {
            return .green
        } else if change < 0 {
            return .red
        } else {
            return .secondary
        }
    }
}

struct MatchTimelineView: View {
    let match: Match
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Timeline")
                .font(.headline)
            
            VStack(alignment: .leading, spacing: 8) {
                TimelineItem(
                    title: "Match Created",
                    time: match.createdAt,
                    icon: "plus.circle",
                    color: .blue
                )
                
                if match.status == .inProgress || match.status == .completed {
                    TimelineItem(
                        title: "Wedstrijd Gestart",
                        time: match.games.first?.startedAt ?? match.createdAt,
                        icon: "play.circle",
                        color: .orange
                    )
                }
                
                if let completedAt = match.completedAt {
                    TimelineItem(
                        title: "Wedstrijd Voltooid",
                        time: completedAt,
                        icon: "checkmark.circle",
                        color: .green
                    )
                }
            }
        }
    }
}

struct TimelineItem: View {
    let title: String
    let time: Date
    let icon: String
    let color: Color
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(color)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(time, formatter: dateTimeFormatter)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
    
    private var dateTimeFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter
    }
}

#Preview {
    NavigationView {
        MatchDetailView(match: Match(
            type: .singles,
            competitionId: UUID(),
            team1Player1: Player(name: "Speler 1", competitionId: UUID()),
            team2Player1: Player(name: "Speler 2", competitionId: UUID())
        ))
    }
    .environmentObject(DataManager())
}

// MARK: - CutoutPlayerImage Component
struct CutoutPlayerImage: View {
    let player: Player
    let size: CGFloat
    let showShineEffect: Bool

    @State private var processedImage: UIImage?
    @State private var isProcessing = false
    @State private var animate = false

    init(player: Player, size: CGFloat, showShineEffect: Bool = false) {
        self.player = player
        self.size = size
        self.showShineEffect = showShineEffect
    }

    var body: some View {
        // Wrap in VStack with bottom alignment to preserve parent alignment
        VStack {
            Spacer(minLength: 0)

            ZStack {
                Group {
                    if let processedImage = processedImage {
                        Image(uiImage: processedImage)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: size, height: size * 1.1) // Rechthoek: zelfde breedte, iets langer
                    } else if let cutoutData = player.cutoutImageData,
                              let cutoutImage = UIImage(data: cutoutData) {
                        // Gebruik opgeslagen cut-out image
                        Image(uiImage: cutoutImage)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: size, height: size * 1.1) // Rechthoek: zelfde breedte, iets langer
                            .onAppear {
                                processedImage = cutoutImage
                            }
                    } else if let imageData = player.profileImageData,
                              let uiImage = UIImage(data: imageData) {
                        // Fallback: genereer cut-out image als deze nog niet bestaat
                        Image(uiImage: uiImage)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: size, height: size * 1.1) // Rechthoek: zelfde breedte, iets langer
                            .onAppear {
                                if !isProcessing {
                                    processImage(uiImage)
                                }
                            }
                    }
                }

                // Shine effect overlay
                if showShineEffect {
                    shineGradient
                        .rotationEffect(.degrees(20))
                        .offset(x: animate ? size * 2 : -size * 2)
                        .mask {
                            Group {
                                if let processedImage = processedImage {
                                    Image(uiImage: processedImage)
                                        .resizable()
                                        .aspectRatio(contentMode: .fit)
                                        .frame(width: size, height: size * 1.1) // Rechthoek: zelfde breedte, iets langer
                                } else if let cutoutData = player.cutoutImageData,
                                          let cutoutImage = UIImage(data: cutoutData) {
                                    Image(uiImage: cutoutImage)
                                        .resizable()
                                        .aspectRatio(contentMode: .fit)
                                        .frame(width: size, height: size * 1.1) // Rechthoek: zelfde breedte, iets langer
                                } else if let imageData = player.profileImageData,
                                          let uiImage = UIImage(data: imageData) {
                                    Image(uiImage: uiImage)
                                        .resizable()
                                        .aspectRatio(contentMode: .fit)
                                        .frame(width: size, height: size * 1.1) // Rechthoek: zelfde breedte, iets langer
                                }
                            }
                        }
                        .onAppear {
                            withAnimation(.linear(duration: 2.0).repeatForever(autoreverses: false)) {
                                animate = true
                            }
                        }
                }
            }
        }
        .frame(height: size) // Ensure consistent height
    }

    private var shineGradient: some View {
        LinearGradient(
            gradient: Gradient(colors: [
                .clear,
                .clear,
                .white.opacity(0.3),
                .white.opacity(0.8),
                .white.opacity(0.3),
                .clear,
                .clear
            ]),
            startPoint: .leading,
            endPoint: .trailing
        )
        .frame(width: size * 0.8, height: size * 1.5)
    }

    private func processImage(_ image: UIImage) {
        isProcessing = true

        Task {
            let cutoutImage = await removeBackground(from: image)

            await MainActor.run {
                self.processedImage = cutoutImage
                self.isProcessing = false
            }
        }
    }

    private func removeBackground(from image: UIImage) async -> UIImage? {
        guard let normalizedImage = normalizeImageOrientation(image) else { return nil }
        guard let inputImage = CIImage(image: normalizedImage) else { return nil }

        return await withCheckedContinuation { continuation in
            let request = VNGeneratePersonSegmentationRequest { request, error in
                guard let observations = request.results as? [VNPixelBufferObservation],
                      let observation = observations.first else {
                    continuation.resume(returning: nil)
                    return
                }

                let maskImage = CIImage(cvPixelBuffer: observation.pixelBuffer)

                // Scale mask to match input image size
                let scaleX = inputImage.extent.width / maskImage.extent.width
                let scaleY = inputImage.extent.height / maskImage.extent.height
                let scaledMask = maskImage.transformed(by: CGAffineTransform(scaleX: scaleX, y: scaleY))

                // Apply mask to create cut-out
                let filter = CIFilter.blendWithMask()
                filter.inputImage = inputImage
                filter.backgroundImage = CIImage.empty()
                filter.maskImage = scaledMask

                guard let maskedImage = filter.outputImage else {
                    continuation.resume(returning: nil)
                    return
                }

                // Find the bounding box of the non-transparent pixels
                let context = CIContext()
                guard let cgMaskedImage = context.createCGImage(maskedImage, from: maskedImage.extent) else {
                    continuation.resume(returning: nil)
                    return
                }

                // Create UIImage from the masked result
                let maskedUIImage = UIImage(cgImage: cgMaskedImage)

                // Crop to the person's bounding box
                if let croppedImage = self.cropToContent(maskedUIImage) {
                    continuation.resume(returning: croppedImage)
                } else {
                    continuation.resume(returning: maskedUIImage)
                }
            }

            request.qualityLevel = .balanced
            request.outputPixelFormat = kCVPixelFormatType_OneComponent8

            guard let cgImage = normalizedImage.cgImage else {
                continuation.resume(returning: nil)
                return
            }

            let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])

            do {
                try handler.perform([request])
            } catch {
                continuation.resume(returning: nil)
            }
        }
    }

    private func cropToContent(_ image: UIImage) -> UIImage? {
        guard let cgImage = image.cgImage else { return nil }

        let width = cgImage.width
        let height = cgImage.height
        let bytesPerPixel = 4
        let bytesPerRow = bytesPerPixel * width
        let bitsPerComponent = 8

        guard let context = CGContext(
            data: nil,
            width: width,
            height: height,
            bitsPerComponent: bitsPerComponent,
            bytesPerRow: bytesPerRow,
            space: CGColorSpaceCreateDeviceRGB(),
            bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue
        ) else { return nil }

        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        guard let data = context.data else { return nil }
        let buffer = data.bindMemory(to: UInt8.self, capacity: width * height * bytesPerPixel)

        var minX = width, maxX = 0, minY = height, maxY = 0

        // Find bounding box of non-transparent pixels
        for y in 0..<height {
            for x in 0..<width {
                let pixelIndex = (y * width + x) * bytesPerPixel
                let alpha = buffer[pixelIndex + 3]

                if alpha > 0 {
                    minX = min(minX, x)
                    maxX = max(maxX, x)
                    minY = min(minY, y)
                    maxY = max(maxY, y)
                }
            }
        }

        // Add padding
        let padding = 10
        minX = max(0, minX - padding)
        maxX = min(width - 1, maxX + padding)
        minY = max(0, minY - padding)
        maxY = min(height - 1, maxY + padding)

        // Make the crop more rectangular/elongated for better vertical fit
        let originalWidth = maxX - minX + 1
        let originalHeight = maxY - minY + 1

        // Calculate target aspect ratio (more vertical/elongated)
        let targetAspectRatio: CGFloat = 0.91 // Width/Height ratio (1/1.1 = zelfde breedte, 10% langer)
        let currentAspectRatio = CGFloat(originalWidth) / CGFloat(originalHeight)

        var finalMinX = minX
        var finalMaxX = maxX
        var finalMinY = minY
        var finalMaxY = maxY

        if currentAspectRatio > targetAspectRatio {
            // Current crop is too wide, make it taller
            let targetWidth = Int(CGFloat(originalHeight) * targetAspectRatio)
            let widthReduction = (originalWidth - targetWidth) / 2
            finalMinX = max(0, minX + widthReduction)
            finalMaxX = min(width - 1, maxX - widthReduction)
        } else {
            // Current crop is too tall, make it wider (less likely for person silhouettes)
            let targetHeight = Int(CGFloat(originalWidth) / targetAspectRatio)
            let heightIncrease = (targetHeight - originalHeight) / 2
            finalMinY = max(0, minY - heightIncrease)
            finalMaxY = min(height - 1, maxY + heightIncrease)
        }

        let cropRect = CGRect(x: finalMinX, y: finalMinY, width: finalMaxX - finalMinX + 1, height: finalMaxY - finalMinY + 1)

        guard let croppedCGImage = cgImage.cropping(to: cropRect) else { return nil }

        return UIImage(cgImage: croppedCGImage)
    }

    private func normalizeImageOrientation(_ image: UIImage) -> UIImage? {
        if image.imageOrientation == .up {
            return image
        }

        let size = image.size
        UIGraphicsBeginImageContextWithOptions(size, false, image.scale)
        defer { UIGraphicsEndImageContext() }

        image.draw(in: CGRect(origin: .zero, size: size))

        guard let normalizedImage = UIGraphicsGetImageFromCurrentImageContext() else {
            return nil
        }

        guard let cgImage = normalizedImage.cgImage else { return nil }
        return UIImage(cgImage: cgImage, scale: image.scale, orientation: .up)
    }
}

import SwiftUI

struct StatisticsView: View {
    @EnvironmentObject var dataManager: DataManager
    @State private var selectedTab: StatisticsTab = .overview
    @Binding var showingSettings: Bool
    
    enum StatisticsTab: String, CaseIterable {
        case overview = "Overview"
        case players = "Players"
        case matches = "Matches"
    }
    
    var body: some View {
        VStack {
            Picker("Statistics", selection: $selectedTab) {
                ForEach(StatisticsTab.allCases, id: \.self) { tab in
                    Text(tab.rawValue).tag(tab)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            .padding()
            
            switch selectedTab {
            case .overview:
                OverviewStatisticsView()
            case .players:
                PlayerStatisticsView()
            case .matches:
                MatchStatisticsView()
            }
        }
        .navigationTitle("Statistics")
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button {
                    showingSettings = true
                } label: {
                    Image(systemName: "gear")
                }
            }
        }
    }
}

struct OverviewStatisticsView: View {
    @EnvironmentObject var dataManager: DataManager
    
    var totalMatches: Int {
        dataManager.matches.filter { $0.status == .completed }.count
    }
    
    var totalGames: Int {
        dataManager.matches
            .filter { $0.status == .completed }
            .flatMap { $0.games }
            .filter { $0.isCompleted }
            .count
    }
    
    var averageMatchDuration: String {
        let completedMatches = dataManager.matches.filter { $0.status == .completed }
        guard !completedMatches.isEmpty else { return "N/A" }
        
        let totalDuration = completedMatches.compactMap { match -> TimeInterval? in
            guard let completedAt = match.completedAt else { return nil }
            return completedAt.timeIntervalSince(match.createdAt)
        }.reduce(0, +)
        
        let averageDuration = totalDuration / Double(completedMatches.count)
        let minutes = Int(averageDuration / 60)
        return "\(minutes) min"
    }
    
    var mostActivePlayer: Player? {
        return dataManager.players.max { $0.matchesPlayed < $1.matchesPlayed }
    }
    
    var body: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                StatCard(
                    title: "Total Matches",
                    value: "\(totalMatches)",
                    subtitle: "Completed",
                    color: .blue,
                    icon: "gamecontroller"
                )

                StatCard(
                    title: "Total Games",
                    value: "\(totalGames)",
                    subtitle: "Played",
                    color: .green,
                    icon: "chart.bar"
                )

                StatCard(
                    title: "Active Players",
                    value: "\(dataManager.players.count)",
                    subtitle: "Registered",
                    color: .orange,
                    icon: "person.2"
                )

                StatCard(
                    title: "Avg. Duration",
                    value: averageMatchDuration,
                    subtitle: "Per match",
                    color: .purple,
                    icon: "clock"
                )
            }
            .padding()
            
            if let mostActive = mostActivePlayer, mostActive.matchesPlayed > 0 {
                VStack(alignment: .leading, spacing: 12) {
                    Text("Meest Actieve Speler")
                        .font(.headline)
                        .padding(.horizontal)
                    
                    HStack {
                        Circle()
                            .fill(Color.blue.gradient)
                            .frame(width: 50, height: 50)
                            .overlay(
                                Text(String(mostActive.name.prefix(1)).uppercased())
                                    .font(.title2)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                            )
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text(mostActive.name)
                                .font(.headline)
                            
                            Text("\(mostActive.matchesPlayed) wedstrijden • \(Int(mostActive.matchWinPercentage))% gewonnen")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            Text("ELO: \(Int(mostActive.eloRating))")
                                .font(.caption)
                                .foregroundColor(.orange)
                        }
                        
                        Spacer()
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    .padding(.horizontal)
                }
            }

            RecentMatchesOverview()
        }
    }
}

struct PlayerStatisticsView: View {
    @EnvironmentObject var dataManager: DataManager
    
    var topPlayersByElo: [Player] {
        return dataManager.players
            .filter { $0.matchesPlayed > 0 }
            .sorted { $0.eloRating > $1.eloRating }
            .prefix(10)
            .map { $0 }
    }
    
    var topPlayersByWins: [Player] {
        return dataManager.players
            .filter { $0.matchesPlayed >= 3 }
            .sorted { $0.matchWinPercentage > $1.matchWinPercentage }
            .prefix(10)
            .map { $0 }
    }
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // Top ELO Players
                VStack(alignment: .leading, spacing: 12) {
                    Text("Top Spelers (ELO)")
                        .font(.headline)
                        .padding(.horizontal)
                    
                    ForEach(Array(topPlayersByElo.enumerated()), id: \.element.id) { index, player in
                        PlayerRankingRow(
                            player: player,
                            rank: index + 1,
                            primaryStat: "\(Int(player.eloRating))",
                            secondaryStat: "\(player.matchesPlayed) wedstrijden"
                        )
                    }
                }
                
                Divider()
                    .padding(.horizontal)
                
                // Top Win Percentage Players
                VStack(alignment: .leading, spacing: 12) {
                    Text("Beste Win Percentage (min. 3 wedstrijden)")
                        .font(.headline)
                        .padding(.horizontal)
                    
                    ForEach(Array(topPlayersByWins.enumerated()), id: \.element.id) { index, player in
                        PlayerRankingRow(
                            player: player,
                            rank: index + 1,
                            primaryStat: "\(Int(player.matchWinPercentage))%",
                            secondaryStat: "\(player.matchesWon)/\(player.matchesPlayed)"
                        )
                    }
                }
            }
            .padding(.vertical)
        }
    }
}

struct MatchStatisticsView: View {
    @EnvironmentObject var dataManager: DataManager
    
    var completedMatches: [Match] {
        return dataManager.matches.filter { $0.status == .completed }
    }
    
    var singlesMatches: Int {
        return completedMatches.filter { $0.type == .singles }.count
    }
    
    var doublesMatches: Int {
        return completedMatches.filter { $0.type == .doubles }.count
    }
    
    var averageGamesPerMatch: Double {
        guard !completedMatches.isEmpty else { return 0 }
        let totalGames = completedMatches.map { $0.games.count }.reduce(0, +)
        return Double(totalGames) / Double(completedMatches.count)
    }
    
    var body: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                StatCard(
                    title: "Singles",
                    value: "\(singlesMatches)",
                    subtitle: "Matches",
                    color: .blue,
                    icon: "person"
                )

                StatCard(
                    title: "Doubles",
                    value: "\(doublesMatches)",
                    subtitle: "Matches",
                    color: .green,
                    icon: "person.2"
                )

                StatCard(
                    title: "Avg. Games",
                    value: String(format: "%.1f", averageGamesPerMatch),
                    subtitle: "Per match",
                    color: .orange,
                    icon: "chart.line.uptrend.xyaxis"
                )

                StatCard(
                    title: "Total Games",
                    value: "\(completedMatches.flatMap { $0.games }.count)",
                    subtitle: "Played",
                    color: .purple,
                    icon: "gamecontroller"
                )
            }
            .padding()
            
            RecentMatchesOverview()
        }
    }
}

struct PlayerRankingRow: View {
    let player: Player
    let rank: Int
    let primaryStat: String
    let secondaryStat: String
    
    var body: some View {
        HStack {
            // Rank
            Text("\(rank)")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(rankColor)
                .frame(width: 30)
            
            // Player info
            HStack {
                Circle()
                    .fill(Color.blue.gradient)
                    .frame(width: 40, height: 40)
                    .overlay(
                        Text(String(player.name.prefix(1)).uppercased())
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                    )
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(player.name)
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Text(secondaryStat)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Text(primaryStat)
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(rankColor)
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(rank <= 3 ? rankColor.opacity(0.1) : Color.clear)
        .cornerRadius(8)
        .padding(.horizontal)
    }
    
    private var rankColor: Color {
        switch rank {
        case 1: return .yellow
        case 2: return .gray
        case 3: return .orange
        default: return .primary
        }
    }
}

struct RecentMatchesOverview: View {
    @EnvironmentObject var dataManager: DataManager
    
    var recentMatches: [Match] {
        return dataManager.completedMatches.prefix(5).map { $0 }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Recent Matches")
                .font(.headline)
                .padding(.horizontal)

            if recentMatches.isEmpty {
                Text("No completed matches yet")
                    .foregroundColor(.secondary)
                    .padding()
            } else {
                ForEach(recentMatches) { match in
                    CompactMatchRow(match: match)
                }
            }
        }
    }
}

struct CompactMatchRow: View {
    let match: Match
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(match.description)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .lineLimit(1)
                
                HStack(spacing: 8) {
                    Text(match.type.displayName)
                        .font(.caption2)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(Color.blue.opacity(0.2))
                        .cornerRadius(4)
                    
                    if let completedAt = match.completedAt {
                        Text(completedAt, formatter: dateFormatter)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Spacer()
            
            Text("\(match.team1GamesWon) - \(match.team2GamesWon)")
                .font(.subheadline)
                .fontWeight(.bold)
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .cornerRadius(8)
        .padding(.horizontal)
    }
    
    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        return formatter
    }
}

#Preview {
    NavigationView {
        StatisticsView(showingSettings: .constant(false))
    }
    .environmentObject(DataManager())
}

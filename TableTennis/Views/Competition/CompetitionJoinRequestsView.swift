import SwiftUI

struct CompetitionJoinRequestsView: View {
    @EnvironmentObject var cloudKitManager: CloudKitManager
    @EnvironmentObject var joinRequestManager: JoinRequestManager
    @State private var joinRequests: [CompetitionJoinRequest] = []
    @State private var responses: [CompetitionJoinResponse] = []
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var showingAlert = false
    @State private var processingRequests: Set<UUID> = [] // Track welke requests worden verwerkt

    let competition: Competition

    /// Alleen join requests die nog geen response hebben
    private var pendingRequests: [CompetitionJoinRequest] {
        let responseRequestIds = Set(responses.map { $0.joinRequestId })
        let pending = joinRequests.filter { !responseRequestIds.contains($0.id) }

        // Debug logging voor filtering
        print("🔍 Filtering debug:")
        print("   📋 Total join requests: \(joinRequests.count)")
        print("   📨 Total responses: \(responses.count)")
        print("   🆔 Response request IDs: \(responseRequestIds)")
        print("   🟡 Pending after filter: \(pending.count)")

        return pending
    }

    var body: some View {
        NavigationView {
            VStack {
                if isLoading {
                    ProgressView("Loading requests...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if pendingRequests.isEmpty {
                    EmptyStateView(
                        title: "No pending requests",
                        subtitle: "All requests have been processed or no requests have been submitted yet",
                        systemImage: "person.2.badge.plus"
                    )
                } else {
                    List {
                        ForEach(pendingRequests) { request in
                            JoinRequestRowView(
                                request: request,
                                isProcessing: processingRequests.contains(request.id),
                                onApprove: { approveRequest(request) },
                                onReject: { rejectRequest(request) }
                            )
                        }
                    }
                }
            }
            .navigationTitle("Requests")
            .navigationBarTitleDisplayMode(.large)
            .refreshable {
                await loadJoinRequests()
            }
            .alert("Fout", isPresented: $showingAlert) {
                Button("OK") { }
            } message: {
                Text(errorMessage ?? "Er is een onbekende fout opgetreden")
            }
        }
        .task {
            await loadJoinRequests()
        }
    }
    
    private func loadJoinRequests() async {
        isLoading = true
        errorMessage = nil

        do {
            let requests = try await cloudKitManager.getJoinRequestsForCompetition(competition)

            // Get responses for all join requests
            let requestIds = requests.map { $0.id }
            let responses = try await cloudKitManager.getJoinRequestResponses(for: requestIds)

            // Debug logging
            print("📋 Loaded \(requests.count) join requests")
            print("📨 Loaded \(responses.count) responses")
            let responseRequestIds = Set(responses.map { $0.joinRequestId })
            let pendingCount = requests.filter { !responseRequestIds.contains($0.id) }.count
            print("🟡 \(pendingCount) pending requests after filtering")

            await MainActor.run {
                self.joinRequests = requests
                self.responses = responses
                self.isLoading = false

                // Als er geen requests zijn en het schema net is aangemaakt, toon een bericht
                if requests.isEmpty {
                    print("ℹ️ No join requests found - this is normal for a new competition")
                }
            }
        } catch {
            await MainActor.run {
                // Geef een gebruiksvriendelijkere foutmelding
                if error.localizedDescription.contains("CompetitionJoinRequest") {
                    self.errorMessage = "Het aanvragen systeem wordt geïnitialiseerd. Probeer het over een paar seconden opnieuw."
                } else {
                    self.errorMessage = error.localizedDescription
                }
                self.isLoading = false
                self.showingAlert = true
            }
        }
    }
    
    private func approveRequest(_ request: CompetitionJoinRequest) {
        Task {
            // Start loading state
            await MainActor.run {
                processingRequests.insert(request.id)
                joinRequestManager.decrementCount() // Optimistische update
            }

            do {
                try await cloudKitManager.approveJoinRequest(request)

                // Wacht even en refresh dan de lijst (CloudKit heeft tijd nodig)
                print("⏱️ Waiting for CloudKit propagation...")
                try await Task.sleep(nanoseconds: 1_500_000_000) // 1.5 seconden
                print("🔄 Refreshing join requests list...")
                await loadJoinRequests() // Refresh de lijst

                // Refresh de badge count voor de zekerheid
                joinRequestManager.refreshCount()

                print("✅ Request approved and UI refreshed")
            } catch {
                // Bij error, herstel de badge count
                await MainActor.run {
                    joinRequestManager.incrementCount()
                    self.errorMessage = error.localizedDescription
                    self.showingAlert = true
                }
                print("❌ Error approving request: \(error)")
            }

            // Stop loading state
            await MainActor.run {
                _ = processingRequests.remove(request.id)
            }
        }
    }
    
    private func rejectRequest(_ request: CompetitionJoinRequest) {
        Task {
            // Start loading state
            await MainActor.run {
                processingRequests.insert(request.id)
                joinRequestManager.decrementCount() // Optimistische update
            }

            do {
                try await cloudKitManager.rejectJoinRequest(request)

                // Wacht even en refresh dan de lijst (CloudKit heeft tijd nodig)
                print("⏱️ Waiting for CloudKit propagation...")
                try await Task.sleep(nanoseconds: 1_500_000_000) // 1.5 seconden
                print("🔄 Refreshing join requests list...")
                await loadJoinRequests() // Refresh de lijst

                // Refresh de badge count voor de zekerheid
                joinRequestManager.refreshCount()

                print("❌ Request rejected and UI refreshed")
            } catch {
                // Bij error, herstel de badge count
                await MainActor.run {
                    joinRequestManager.incrementCount()
                    self.errorMessage = error.localizedDescription
                    self.showingAlert = true
                }
                print("❌ Error rejecting request: \(error)")
            }

            // Stop loading state
            await MainActor.run {
                _ = processingRequests.remove(request.id)
            }
        }
    }
}

struct JoinRequestRowView: View {
    let request: CompetitionJoinRequest
    let isProcessing: Bool
    let onApprove: () -> Void
    let onReject: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(request.requesterName)
                        .font(.headline)
                    
                    Text("Requested on \(request.requestedAt, style: .date)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                HStack(spacing: 4) {
                    Image(systemName: request.status.systemImage)
                        .foregroundColor(statusColor)
                    Text(request.status.displayName)
                        .font(.caption)
                        .foregroundColor(statusColor)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(statusColor.opacity(0.1))
                .cornerRadius(8)
            }
            
            if request.isPending {
                HStack(spacing: 12) {
                    Button(action: onApprove) {
                        HStack(spacing: 4) {
                            if isProcessing {
                                ProgressView()
                                    .scaleEffect(0.8)
                            }
                            Text("Approve")
                        }
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.small)
                    .disabled(isProcessing)

                    Button(action: onReject) {
                        HStack(spacing: 4) {
                            if isProcessing {
                                ProgressView()
                                    .scaleEffect(0.8)
                            }
                            Text("Reject")
                        }
                    }
                    .buttonStyle(.bordered)
                    .controlSize(.small)
                    .foregroundColor(.red)
                    .disabled(isProcessing)
                }
            } else if let reviewedAt = request.reviewedAt {
                Text("Reviewed on \(reviewedAt, style: .date)")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
    }
    
    private var statusColor: Color {
        switch request.status {
        case .pending:
            return .orange
        case .approved:
            return .green
        case .rejected:
            return .red
        }
    }
}

#Preview {
    let sampleCompetition = Competition(name: "Test Competition", description: "Test", createdBy: "test-user")
    
    CompetitionJoinRequestsView(competition: sampleCompetition)
        .environmentObject(CloudKitManager.shared)
}

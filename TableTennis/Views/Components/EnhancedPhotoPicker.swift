import SwiftUI
import PhotosUI

struct EnhancedPhotoPicker: View {
    @Binding var selectedImageData: Data?
    @Binding var cutoutImageData: Data?
    @State private var selectedItem: PhotosPickerItem?
    @State private var isLoading = false
    @State private var isProcessingCutout = false
    @State private var showingPhotoPicker = false
    @State private var showingCropView = false
    @State private var showingCutoutCropView = false
    @State private var selectedUIImage: UIImage?
    @State private var croppedUIImage: UIImage?

    let placeholder: String
    let maxImageSize: CGFloat
    
    init(selectedImageData: Binding<Data?>, cutoutImageData: Binding<Data?>, placeholder: String = "Selecteer foto", maxImageSize: CGFloat = 100) {
        self._selectedImageData = selectedImageData
        self._cutoutImageData = cutoutImageData
        self.placeholder = placeholder
        self.maxImageSize = maxImageSize
    }
    
    var body: some View {
        VStack(spacing: 12) {
            // Foto weergave of placeholder
            ZStack {
                if let imageData = selectedImageData,
                   let uiImage = UIImage(data: imageData) {
                    // Toon geselecteerde foto
                    Image(uiImage: uiImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: maxImageSize, height: maxImageSize)
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                        )
                } else {
                    // Placeholder cirkel
                    Circle()
                        .fill(Color.gray.opacity(0.2))
                        .frame(width: maxImageSize, height: maxImageSize)
                        .overlay(
                            VStack(spacing: 4) {
                                Image(systemName: "camera.fill")
                                    .font(.title2)
                                    .foregroundColor(.gray)
                                Text("Foto")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        )
                }
                
                // Loading indicator
                if isLoading || isProcessingCutout {
                    Circle()
                        .fill(Color.black.opacity(0.6))
                        .frame(width: maxImageSize, height: maxImageSize)
                        .overlay(
                            VStack(spacing: 8) {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(0.8)
                                
                                Text(isProcessingCutout ? "Verwerken..." : "Laden...")
                                    .font(.caption2)
                                    .foregroundColor(.white)
                            }
                        )
                }
            }
            .onTapGesture {
                showingPhotoPicker = true
            }
            
            // Actie knoppen
            HStack(spacing: 16) {
                Button(placeholder) {
                    showingPhotoPicker = true
                }
                .font(.caption)
                .foregroundColor(.blue)
                
                if selectedImageData != nil {
                    Button("Verwijder") {
                        selectedImageData = nil
                        cutoutImageData = nil
                    }
                    .font(.caption)
                    .foregroundColor(.red)
                }
            }
            
            // Status indicator voor cut-out
            if cutoutImageData != nil {
                HStack(spacing: 4) {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .font(.caption)
                    Text("Cut-out gegenereerd")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
        .photosPicker(isPresented: $showingPhotoPicker, selection: $selectedItem, matching: .images)
        .sheet(isPresented: $showingCropView) {
            if let image = selectedUIImage {
                ImageCropView(image: image) { croppedImage in
                    if let croppedImage = croppedImage {
                        let compressedData = compressImage(croppedImage, maxSizeKB: 500)
                        selectedImageData = compressedData

                        // Ga naar cutout crop met originele afbeelding
                        showingCropView = false
                        showingCutoutCropView = true
                    } else {
                        showingCropView = false
                        selectedUIImage = nil
                    }
                }
            }
        }
        .sheet(isPresented: $showingCutoutCropView) {
            if let image = selectedUIImage {
                CutoutCropView(image: image) { cutoutCroppedImage in
                    if let cutoutCroppedImage = cutoutCroppedImage {
                        // Genereer cut-out image van de cutout crop
                        Task {
                            await generateCutoutImage(from: cutoutCroppedImage)
                        }
                    }
                    showingCutoutCropView = false
                    selectedUIImage = nil
                    croppedUIImage = nil
                }
            }
        }
        .onChange(of: selectedItem) { _, newItem in
            Task {
                await loadSelectedImage(from: newItem)
            }
        }
    }
    
    @MainActor
    private func loadSelectedImage(from item: PhotosPickerItem?) async {
        guard let item = item else { return }

        isLoading = true
        defer { isLoading = false }

        do {
            if let data = try await item.loadTransferable(type: Data.self) {
                if let uiImage = UIImage(data: data) {
                    // Toon crop view in plaats van direct opslaan
                    selectedUIImage = uiImage
                    showingCropView = true
                }
            }
        } catch {
            print("Error loading selected image: \(error)")
        }
    }
    
    @MainActor
    private func generateCutoutImage(from image: UIImage) async {
        isProcessingCutout = true
        defer { isProcessingCutout = false }
        
        do {
            if let cutoutImage = await ImageProcessingService.shared.generateCutoutImage(from: image) {
                cutoutImageData = ImageProcessingService.shared.cutoutImageToData(cutoutImage)
            }
        }
    }
    
    private func compressImage(_ image: UIImage, maxSizeKB: Int) -> Data? {
        let maxBytes = maxSizeKB * 1024
        var compression: CGFloat = 1.0
        var imageData = image.jpegData(compressionQuality: compression)
        
        // Reduceer de compressie tot de gewenste bestandsgrootte is bereikt
        while let data = imageData, data.count > maxBytes && compression > 0.1 {
            compression -= 0.1
            imageData = image.jpegData(compressionQuality: compression)
        }
        
        return imageData
    }
}

#Preview {
    @State var imageData: Data? = nil
    @State var cutoutData: Data? = nil
    
    return VStack {
        EnhancedPhotoPicker(
            selectedImageData: $imageData,
            cutoutImageData: $cutoutData,
            placeholder: "Selecteer foto",
            maxImageSize: 100
        )
        .padding()
    }
}

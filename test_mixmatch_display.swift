import Foundation

// Test om te valideren dat Mix & Match wedstrijden correct worden weergegeven
// in de PlayerDetailView recent matches sectie

print("=== MIX & MATCH DISPLAY TEST ===")
print("Recent matches moeten games won/total tonen voor Mix & Match")

// Mock data structures
struct MockGame {
    let isCompleted: Bool
    let winner: TeamSide?
    let mixMatchTeam1Players: [MockPlayer]?
    let mixMatchTeam2Players: [MockPlayer]?
}

enum TeamSide {
    case team1, team2
}

struct MockPlayer {
    let id: UUID
    let name: String
}

struct MockMatch {
    let type: MatchType
    let games: [MockGame]
    let team1GamesWon: Int
    let team2GamesWon: Int
}

enum MatchType {
    case singles, doubles, mixMatch
}

// Test spelers
let joost = MockPlayer(id: UUID(), name: "Joost")
let richard = MockPlayer(id: UUID(), name: "<PERSON>")
let darius = MockPlayer(id: UUI<PERSON>(), name: "<PERSON>")
let gavin = MockPlayer(id: UUID(), name: "<PERSON>")

// Test Mix & Match wedstrijd
let mixMatchGames = [
    MockGame(
        isCompleted: true,
        winner: .team1,
        mixMatchTeam1Players: [joost, richard],
        mixMatchTeam2Players: [darius, gavin]
    ),
    MockGame(
        isCompleted: true,
        winner: .team2,
        mixMatchTeam1Players: [darius, richard],
        mixMatchTeam2Players: [gavin, joost]
    ),
    MockGame(
        isCompleted: true,
        winner: .team2,
        mixMatchTeam1Players: [gavin, richard],
        mixMatchTeam2Players: [darius, joost]
    )
]

let mixMatchMatch = MockMatch(
    type: .mixMatch,
    games: mixMatchGames,
    team1GamesWon: 1,
    team2GamesWon: 2
)

// Test normale wedstrijd
let normalMatch = MockMatch(
    type: .doubles,
    games: [],
    team1GamesWon: 3,
    team2GamesWon: 1
)

// Simuleer matchResultText logica
func getMatchResultText(match: MockMatch, player: MockPlayer) -> String {
    if match.type == .mixMatch {
        // Voor Mix & Match: toon aantal gewonnen games door deze speler
        let gamesWon = match.games.filter { game in
            guard game.isCompleted,
                  let winner = game.winner,
                  let team1Players = game.mixMatchTeam1Players,
                  let team2Players = game.mixMatchTeam2Players else { return false }
            
            // Check of deze speler in het winnende team zat
            let playerInTeam1 = team1Players.contains { $0.id == player.id }
            let playerInTeam2 = team2Players.contains { $0.id == player.id }
            
            return (playerInTeam1 && winner == .team1) || (playerInTeam2 && winner == .team2)
        }.count
        
        return "\(gamesWon)/\(match.games.count)"
    } else {
        // Voor andere match types: toon normale uitslag
        return "\(match.team1GamesWon) - \(match.team2GamesWon)"
    }
}

print("\n" + String(repeating: "=", count: 60))
print("TEST RESULTS")
print(String(repeating: "=", count: 60))

// Test alle spelers voor Mix & Match wedstrijd
let testPlayers = [joost, richard, darius, gavin]

print("MIX & MATCH WEDSTRIJD:")
print("Game 1: Joost/Richard vs Darius/Gavin (Team 1 wins)")
print("Game 2: Darius/Richard vs Gavin/Joost (Team 2 wins)")
print("Game 3: Gavin/Richard vs Darius/Joost (Team 2 wins)")
print()

for player in testPlayers {
    let resultText = getMatchResultText(match: mixMatchMatch, player: player)
    print("\(player.name): \(resultText)")
}

print("\nNORMALE WEDSTRIJD (Doubles):")
for player in testPlayers {
    let resultText = getMatchResultText(match: normalMatch, player: player)
    print("\(player.name): \(resultText)")
}

print("\n" + String(repeating: "=", count: 60))
print("VERWACHTE RESULTATEN:")
print("Mix & Match:")
print("- Joost: 2/3 (won games 1 & 3)")
print("- Richard: 1/3 (won game 1)")
print("- Darius: 1/3 (won game 3)")
print("- Gavin: 2/3 (won games 2 & 3)")
print()
print("Normale wedstrijd:")
print("- Alle spelers: 3 - 1 (standaard uitslag)")

print("\n" + String(repeating: "=", count: 60))
print("LOADING SPINNER TEST:")
print("✅ DataManager heeft nu @Published var isRecalculatingStatistics")
print("✅ SettingsView toont spinner tijdens recalculation")
print("✅ DebugStatisticsView toont spinner tijdens recalculation")
print("✅ Button wordt disabled tijdens recalculation")

print("\nIMPLEMENTATIE DETAILS:")
print("1. Mix & Match toont: 'games_won/total_games'")
print("2. Andere match types tonen: 'team1_games - team2_games'")
print("3. Spinner wordt getoond zolang isRecalculatingStatistics = true")
print("4. Button wordt disabled tijdens recalculation")

print("\nVOLGENDE STAPPEN:")
print("🚀 Test de app met een echte Mix & Match wedstrijd")
print("🚀 Controleer dat recent matches correct worden weergegeven")
print("🚀 Test de force recalculate button met spinner")

import SwiftUI

struct AddPlayerView: View {
    @EnvironmentObject var dataManager: DataManager
    @EnvironmentObject var cloudKitManager: CloudKitManager
    @Environment(\.presentationMode) var presentationMode

    @State private var playerName = ""
    @State private var profileImageData: Data?
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("Speler Informatie")) {
                    TextField("Naam", text: $playerName)
                        .textFieldStyle(RoundedBorderTextFieldStyle())

                    HStack {
                        Text("ELO Rating:")
                            .font(.subheadline)
                        Spacer()
                        Text("1200")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.blue)
                    }
                }

                Section(header: Text("Profielfoto")) {
                    PhotoPicker(selectedImageData: $profileImageData, maxImageSize: 80)
                        .frame(maxWidth: .infinity, alignment: .center)
                }

                Section(footer: Text("ELO rating wordt automatisch aangepast op basis van wedstrijdresultaten.")) {
                    EmptyView()
                }
            }
            .navigationTitle("Nieuwe Speler")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Annuleren") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Opslaan") {
                        savePlayer()
                    }
                    .disabled(playerName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("Fout"),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("OK"))
                )
            }
        }
    }
    
    private func savePlayer() {
        let trimmedName = playerName.trimmingCharacters(in: .whitespacesAndNewlines)

        // Validatie
        guard !trimmedName.isEmpty else {
            alertMessage = "Voer een geldige naam in."
            showingAlert = true
            return
        }

        guard let competition = cloudKitManager.currentCompetition else {
            alertMessage = "Geen competitie geselecteerd."
            showingAlert = true
            return
        }

        // Controleer of speler al bestaat
        if dataManager.players.contains(where: { $0.name.lowercased() == trimmedName.lowercased() }) {
            alertMessage = "Een speler met deze naam bestaat al."
            showingAlert = true
            return
        }

        // Maak nieuwe speler aan met vaste ELO rating van 1200
        let newPlayer = Player(name: trimmedName, competitionId: competition.id, eloRating: 1200.0, profileImageData: profileImageData)
        dataManager.addPlayer(newPlayer)

        presentationMode.wrappedValue.dismiss()
    }
}

#Preview {
    AddPlayerView()
        .environmentObject(DataManager())
}

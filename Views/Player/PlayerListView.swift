import SwiftUI

struct PlayerListView: View {
    @EnvironmentObject var dataManager: DataManager
    @State private var showingAddPlayer = false
    @State private var searchText = ""
    @State private var sortOption: PlayerSortOption = .name

    enum PlayerSortOption: String, CaseIterable {
        case name = "Naam"
        case elo = "ELO Rating"
        case matches = "Wedstrijden"
        case winPercentage = "Win %"
    }

    var filteredAndSortedPlayers: [Player] {
        let filtered = searchText.isEmpty ? dataManager.players :
            dataManager.players.filter { $0.name.localizedCaseInsensitiveContains(searchText) }

        return filtered.sorted { player1, player2 in
            switch sortOption {
            case .name:
                return player1.name < player2.name
            case .elo:
                return player1.eloRating > player2.eloRating
            case .matches:
                return player1.matchesPlayed > player2.matchesPlayed
            case .winPercentage:
                return player1.matchWinPercentage > player2.matchWinPercentage
            }
        }
    }

    var body: some View {
        VStack {
            // Search and Sort Controls
            VStack(spacing: 12) {
                SearchBar(text: $searchText)

                Picker("Sorteer op", selection: $sortOption) {
                    ForEach(PlayerSortOption.allCases, id: \.self) { option in
                        Text(option.rawValue).tag(option)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            .padding(.horizontal)

            // Players List
            if filteredAndSortedPlayers.isEmpty {
                EmptyStateView(
                    title: searchText.isEmpty ? "Geen spelers" : "Geen resultaten",
                    subtitle: searchText.isEmpty ?
                        "Voeg je eerste speler toe om te beginnen" :
                        "Probeer een andere zoekterm",
                    systemImage: "person.2"
                )
            } else {
                List {
                    ForEach(filteredAndSortedPlayers) { player in
                        NavigationLink(destination: PlayerDetailView(player: player)) {
                            PlayerRowView(player: player)
                        }
                    }
                    .onDelete(perform: deletePlayers)
                }
                .refreshable {
                    await dataManager.loadData()
                }
            }
        }
        .navigationTitle("Spelers")
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: { showingAddPlayer = true }) {
                    Image(systemName: "plus")
                }
            }
        }
        .sheet(isPresented: $showingAddPlayer) {
            AddPlayerView()
        }
    }

    private func deletePlayers(offsets: IndexSet) {
        for index in offsets {
            let player = filteredAndSortedPlayers[index]
            dataManager.deletePlayer(player)
        }
    }
}

struct PlayerRowView: View {
    let player: Player
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(player.name)
                    .font(.headline)
                
                HStack(spacing: 16) {
                    Label("\(Int(player.eloRating))", systemImage: "star.fill")
                        .font(.caption)
                        .foregroundColor(.orange)
                    
                    Label("\(player.matchesPlayed)", systemImage: "gamecontroller")
                        .font(.caption)
                        .foregroundColor(.blue)
                    
                    if player.matchesPlayed > 0 {
                        Label("\(Int(player.matchWinPercentage))%", systemImage: "chart.line.uptrend.xyaxis")
                            .font(.caption)
                            .foregroundColor(.green)
                    }
                }
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text("\(player.matchesWon)W - \(player.matchesLost)L")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                if player.matchesPlayed > 0 {
                    Text("\(Int(player.matchWinPercentage))%")
                        .font(.caption2)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(winPercentageColor(player.matchWinPercentage))
                        .foregroundColor(.white)
                        .cornerRadius(4)
                }
            }
        }
        .padding(.vertical, 4)
    }
    
    private func winPercentageColor(_ percentage: Double) -> Color {
        switch percentage {
        case 70...:
            return .green
        case 50..<70:
            return .orange
        default:
            return .red
        }
    }
}

struct SearchBar: View {
    @Binding var text: String
    
    var body: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("Zoek spelers...", text: $text)
                .textFieldStyle(RoundedBorderTextFieldStyle())
            
            if !text.isEmpty {
                Button("Wissen") {
                    text = ""
                }
                .foregroundColor(.blue)
            }
        }
    }
}

struct EmptyStateView: View {
    let title: String
    let subtitle: String
    let systemImage: String
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: systemImage)
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            VStack(spacing: 8) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(subtitle)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

#Preview {
    PlayerListView()
        .environmentObject(DataManager())
}

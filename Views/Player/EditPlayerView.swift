import SwiftUI

struct EditPlayerView: View {
    @EnvironmentObject var dataManager: DataManager
    @Environment(\.presentationMode) var presentationMode
    
    @State private var playerName: String
    @State private var eloRating: Double
    @State private var profileImageData: Data?
    @State private var showingAlert = false
    @State private var alertMessage = ""

    let player: Player

    init(player: Player) {
        self.player = player
        self._playerName = State(initialValue: player.name)
        self._eloRating = State(initialValue: player.eloRating)
        self._profileImageData = State(initialValue: player.profileImageData)
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("Speler Informatie")) {
                    TextField("Naam", text: $playerName)
                        .textFieldStyle(RoundedBorderTextFieldStyle())

                    VStack(alignment: .leading, spacing: 8) {
                        Text("ELO Rating: \(Int(eloRating))")
                            .font(.subheadline)

                        Slider(value: $eloRating, in: 800...2000, step: 50)

                        HStack {
                            Text("800")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Spacer()
                            Text("1200 (Standaard)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Spacer()
                            Text("2000")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }

                Section(header: Text("Profielfoto")) {
                    PhotoPicker(selectedImageData: $profileImageData, maxImageSize: 80)
                        .frame(maxWidth: .infinity, alignment: .center)
                }
                
                Section(header: Text("Statistieken"), footer: Text("Deze statistieken worden automatisch bijgewerkt op basis van wedstrijdresultaten.")) {
                    HStack {
                        Text("Wedstrijden gespeeld")
                        Spacer()
                        Text("\(player.matchesPlayed)")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("Wedstrijden gewonnen")
                        Spacer()
                        Text("\(player.matchesWon)")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("Win percentage")
                        Spacer()
                        Text("\(Int(player.matchWinPercentage))%")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("Games gespeeld")
                        Spacer()
                        Text("\(player.gamesPlayed)")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("Games gewonnen")
                        Spacer()
                        Text("\(player.gamesWon)")
                            .foregroundColor(.secondary)
                    }
                }
                
                Section(footer: Text("Let op: Het aanpassen van de ELO rating heeft invloed op toekomstige wedstrijdberekeningen.")) {
                    EmptyView()
                }
            }
            .navigationTitle("Bewerk Speler")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Annuleren") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Opslaan") {
                        savePlayer()
                    }
                    .disabled(playerName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("Fout"),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("OK"))
                )
            }
        }
    }
    
    private func savePlayer() {
        let trimmedName = playerName.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Validatie
        guard !trimmedName.isEmpty else {
            alertMessage = "Voer een geldige naam in."
            showingAlert = true
            return
        }
        
        // Controleer of speler al bestaat (behalve de huidige speler)
        if dataManager.players.contains(where: { $0.name.lowercased() == trimmedName.lowercased() && $0.id != player.id }) {
            alertMessage = "Een andere speler met deze naam bestaat al."
            showingAlert = true
            return
        }
        
        // Update speler
        var updatedPlayer = player
        updatedPlayer.name = trimmedName
        updatedPlayer.eloRating = eloRating
        updatedPlayer.profileImageData = profileImageData

        dataManager.updatePlayer(updatedPlayer)
        
        presentationMode.wrappedValue.dismiss()
    }
}

#Preview {
    EditPlayerView(player: Player(name: "Test Speler", competitionId: UUID()))
        .environmentObject(DataManager())
}

import SwiftUI

struct PlayerAvatar: View {
    let player: Player
    let size: CGFloat
    let showCrown: Bool
    
    init(player: Player, size: CGFloat = 40, showCrown: Bool = false) {
        self.player = player
        self.size = size
        self.showCrown = showCrown
    }
    
    var body: some View {
        ZStack {
            if let imageData = player.profileImageData,
               let uiImage = UIImage(data: imageData) {
                // Toon profielfoto
                Image(uiImage: uiImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: size, height: size)
                    .clipShape(Circle())
                    .overlay(
                        Circle()
                            .stroke(showCrown ? Color.yellow : Color.gray.opacity(0.3), lineWidth: showCrown ? 2 : 1)
                    )
            } else {
                // Fallback naar initialen
                Circle()
                    .fill(showCrown ? 
                          Color(red: 0.95, green: 0.90, blue: 0.75).gradient :
                          Color.blue.gradient)
                    .frame(width: size, height: size)
                    .overlay(
                        Text(String(player.name.prefix(1)).uppercased())
                            .font(fontForSize(size))
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                    )
            }
            
            // Crown voor #1 speler
            if showCrown {
                Image(systemName: "crown.fill")
                    .font(crownFontForSize(size))
                    .foregroundColor(.yellow)
                    .shadow(color: .orange, radius: 2)
                    .offset(y: -size * 0.55)
            }
        }
    }
    
    private func fontForSize(_ size: CGFloat) -> Font {
        switch size {
        case 0..<30:
            return .caption
        case 30..<50:
            return .headline
        case 50..<80:
            return .title2
        default:
            return .largeTitle
        }
    }
    
    private func crownFontForSize(_ size: CGFloat) -> Font {
        switch size {
        case 0..<30:
            return .caption2
        case 30..<50:
            return .caption
        case 50..<80:
            return .title3
        default:
            return .title2
        }
    }
}

#Preview {
    let sampleCompetitionId = UUID()
    
    VStack(spacing: 20) {
        // Speler zonder foto
        PlayerAvatar(player: Player(name: "John Doe", competitionId: sampleCompetitionId), size: 40)
        
        // Speler met crown
        PlayerAvatar(player: Player(name: "Jane Smith", competitionId: sampleCompetitionId), size: 80, showCrown: true)
        
        // Verschillende groottes
        HStack(spacing: 10) {
            PlayerAvatar(player: Player(name: "Small", competitionId: sampleCompetitionId), size: 30)
            PlayerAvatar(player: Player(name: "Medium", competitionId: sampleCompetitionId), size: 50)
            PlayerAvatar(player: Player(name: "Large", competitionId: sampleCompetitionId), size: 80)
        }
    }
    .padding()
}

import SwiftUI
import PhotosUI

struct PhotoPicker: View {
    @Binding var selectedImageData: Data?
    @State private var selectedItem: PhotosPickerItem?
    @State private var isLoading = false
    @State private var showingPhotoPicker = false
    @State private var showingCropView = false
    @State private var selectedUIImage: UIImage?

    let placeholder: String
    let maxImageSize: CGFloat
    
    init(selectedImageData: Binding<Data?>, placeholder: String = "Selecteer foto", maxImageSize: CGFloat = 100) {
        self._selectedImageData = selectedImageData
        self.placeholder = placeholder
        self.maxImageSize = maxImageSize
    }
    
    var body: some View {
        VStack(spacing: 12) {
            // Foto weergave of placeholder
            ZStack {
                if let imageData = selectedImageData,
                   let uiImage = UIImage(data: imageData) {
                    // Toon geselecteerde foto
                    Image(uiImage: uiImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: maxImageSize, height: maxImageSize)
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                        )
                } else {
                    // Placeholder cirkel
                    Circle()
                        .fill(Color.gray.opacity(0.2))
                        .frame(width: maxImageSize, height: maxImageSize)
                        .overlay(
                            VStack(spacing: 4) {
                                Image(systemName: "camera.fill")
                                    .font(.title2)
                                    .foregroundColor(.gray)
                                Text("Foto")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        )
                }
                
                // Loading indicator
                if isLoading {
                    Circle()
                        .fill(Color.black.opacity(0.3))
                        .frame(width: maxImageSize, height: maxImageSize)
                        .overlay(
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        )
                }
            }
            
            // Knoppen
            HStack(spacing: 12) {
                // Selecteer foto knop
                Button(action: {
                    showingPhotoPicker = true
                }) {
                    Label(selectedImageData == nil ? placeholder : "Wijzig foto", systemImage: "photo")
                        .font(.caption)
                        .foregroundColor(.blue)
                }
                .disabled(isLoading)
                
                // Verwijder foto knop (alleen tonen als er een foto is)
                if selectedImageData != nil {
                    Button(action: {
                        selectedImageData = nil
                        selectedItem = nil
                    }) {
                        Label("Verwijder", systemImage: "trash")
                            .font(.caption)
                            .foregroundColor(.red)
                    }
                    .disabled(isLoading)
                }
            }
        }
        .photosPicker(isPresented: $showingPhotoPicker, selection: $selectedItem, matching: .images)
        .sheet(isPresented: $showingCropView) {
            if let image = selectedUIImage {
                ImageCropView(image: image) { croppedImage in
                    if let croppedImage = croppedImage {
                        let compressedData = compressImage(croppedImage, maxSizeKB: 500)
                        selectedImageData = compressedData
                    }
                    showingCropView = false
                    selectedUIImage = nil
                }
            }
        }
        .onChange(of: selectedItem) { _, newItem in
            Task {
                await loadSelectedImage(from: newItem)
            }
        }
    }
    
    @MainActor
    private func loadSelectedImage(from item: PhotosPickerItem?) async {
        guard let item = item else { return }

        isLoading = true
        defer { isLoading = false }

        do {
            if let data = try await item.loadTransferable(type: Data.self) {
                if let uiImage = UIImage(data: data) {
                    // Toon crop view in plaats van direct opslaan
                    selectedUIImage = uiImage
                    showingCropView = true
                }
            }
        } catch {
            print("Error loading selected image: \(error)")
        }
    }
    
    private func compressImage(_ image: UIImage, maxSizeKB: Int) -> Data? {
        let maxBytes = maxSizeKB * 1024
        var compression: CGFloat = 1.0
        var imageData = image.jpegData(compressionQuality: compression)
        
        // Reduceer de compressie tot de gewenste bestandsgrootte is bereikt
        while let data = imageData, data.count > maxBytes && compression > 0.1 {
            compression -= 0.1
            imageData = image.jpegData(compressionQuality: compression)
        }
        
        // Als de afbeelding nog steeds te groot is, verklein dan de afbeelding zelf
        if let data = imageData, data.count > maxBytes {
            let targetSize = CGSize(width: 300, height: 300)
            let resizedImage = resizeImage(image, targetSize: targetSize)
            imageData = resizedImage.jpegData(compressionQuality: 0.8)
        }
        
        return imageData
    }
    
    private func resizeImage(_ image: UIImage, targetSize: CGSize) -> UIImage {
        let size = image.size
        let widthRatio = targetSize.width / size.width
        let heightRatio = targetSize.height / size.height
        let ratio = min(widthRatio, heightRatio)
        
        let newSize = CGSize(width: size.width * ratio, height: size.height * ratio)
        
        UIGraphicsBeginImageContextWithOptions(newSize, false, 0.0)
        image.draw(in: CGRect(origin: .zero, size: newSize))
        let newImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        return newImage ?? image
    }
}

#Preview {
    struct PhotoPickerPreview: View {
        @State private var imageData: Data?
        
        var body: some View {
            VStack(spacing: 20) {
                PhotoPicker(selectedImageData: $imageData)
                
                Text("Geselecteerde foto: \(imageData != nil ? "Ja" : "Nee")")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
        }
    }
    
    return PhotoPickerPreview()
}

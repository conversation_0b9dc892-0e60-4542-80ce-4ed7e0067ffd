import Foundation

// Test nieuwe win/loss verdeling logica:
// - Bij WINST: Verdeling gebaseerd op teamgenoot ELO (hogere teamgenoot = meer punten)
// - Bij VERLIES: Verdeling gebaseerd op eigen ELO (hogere eigen ELO = meer verlies)
// - Geen 0.75 factor

struct TestPlayer {
    let name: String
    var eloRating: Double
}

struct EloCalculator {
    static let defaultKFactor: Double = 32.0
    
    static func expectedScore(playerRating: Double, opponentRating: Double) -> Double {
        let ratingDifference = opponentRating - playerRating
        return 1.0 / (1.0 + pow(10.0, ratingDifference / 400.0))
    }
    
    static func ratingChange(currentRating: Double,
                           opponentRating: Double,
                           actualScore: Double,
                           kFactor: Double = defaultKFactor) -> Double {
        let expectedScore = expectedScore(playerRating: currentRating, opponentRating: opponentRating)
        return kFactor * (actualScore - expectedScore)
    }
}

print("=== WIN/LOSS DISTRIBUTION TEST ===")
print("NEW LOGIC:")
print("- WINST: Verdeling gebaseerd op teamgenoot ELO")
print("- VERLIES: Verdeling gebaseerd op eigen ELO")
print("- Geen 0.75 factor")

// Test scenario: Verschillende ELO ratings
let joost = TestPlayer(name: "Joost", eloRating: 1300)    // Hoog
let richard = TestPlayer(name: "Richard", eloRating: 1100) // Laag
let darius = TestPlayer(name: "Darius", eloRating: 1250)  // Middel-hoog
let gavin = TestPlayer(name: "Gavin", eloRating: 1150)    // Middel-laag

print("\nSpelers:")
print("Joost: 1300 (hoog)")
print("Richard: 1100 (laag)")
print("Darius: 1250 (middel-hoog)")
print("Gavin: 1150 (middel-laag)")

func calculateGameResult(team1Player1: TestPlayer, team1Player2: TestPlayer,
                        team2Player1: TestPlayer, team2Player2: TestPlayer,
                        team1Score: Int, team2Score: Int) -> [String: Double] {
    
    // Team gemiddeldes
    let team1Average = (team1Player1.eloRating + team1Player2.eloRating) / 2.0
    let team2Average = (team2Player1.eloRating + team2Player2.eloRating) / 2.0
    
    // Score ratios
    let totalPoints = team1Score + team2Score
    let team1ScoreRatio = Double(team1Score) / Double(totalPoints)
    let team2ScoreRatio = Double(team2Score) / Double(totalPoints)
    
    // Team ELO wijzigingen (zonder 0.75 factor)
    let team1Change = EloCalculator.ratingChange(
        currentRating: team1Average,
        opponentRating: team2Average,
        actualScore: team1ScoreRatio,
        kFactor: EloCalculator.defaultKFactor
    )
    
    let team2Change = EloCalculator.ratingChange(
        currentRating: team2Average,
        opponentRating: team1Average,
        actualScore: team2ScoreRatio,
        kFactor: EloCalculator.defaultKFactor
    )
    
    var results: [String: Double] = [:]
    
    // Team 1 verdeling
    if team1Change > 0 {
        // WINST: Gebaseerd op teamgenoot ELO
        let totalTeammateElo = team1Player1.eloRating + team1Player2.eloRating
        let player1Share = team1Player2.eloRating / totalTeammateElo // Gebaseerd op teammate
        let player2Share = team1Player1.eloRating / totalTeammateElo // Gebaseerd op teammate
        
        results[team1Player1.name] = team1Change * player1Share
        results[team1Player2.name] = team1Change * player2Share
    } else {
        // VERLIES: Gebaseerd op eigen ELO
        let totalOwnElo = team1Player1.eloRating + team1Player2.eloRating
        let player1Share = team1Player1.eloRating / totalOwnElo // Gebaseerd op eigen ELO
        let player2Share = team1Player2.eloRating / totalOwnElo // Gebaseerd op eigen ELO
        
        results[team1Player1.name] = team1Change * player1Share
        results[team1Player2.name] = team1Change * player2Share
    }
    
    // Team 2 verdeling
    if team2Change > 0 {
        // WINST: Gebaseerd op teamgenoot ELO
        let totalTeammateElo = team2Player1.eloRating + team2Player2.eloRating
        let player1Share = team2Player2.eloRating / totalTeammateElo // Gebaseerd op teammate
        let player2Share = team2Player1.eloRating / totalTeammateElo // Gebaseerd op teammate
        
        results[team2Player1.name] = team2Change * player1Share
        results[team2Player2.name] = team2Change * player2Share
    } else {
        // VERLIES: Gebaseerd op eigen ELO
        let totalOwnElo = team2Player1.eloRating + team2Player2.eloRating
        let player1Share = team2Player1.eloRating / totalOwnElo // Gebaseerd op eigen ELO
        let player2Share = team2Player2.eloRating / totalOwnElo // Gebaseerd op eigen ELO
        
        results[team2Player1.name] = team2Change * player1Share
        results[team2Player2.name] = team2Change * player2Share
    }
    
    return results
}

// Test 1: Joost/Richard vs Darius/Gavin (11-5)
print("\n" + String(repeating: "=", count: 50))
print("TEST 1: Joost(1300)/Richard(1100) vs Darius(1250)/Gavin(1150)")
print("Score: 11-5 (Joost/Richard winnen)")

let result1 = calculateGameResult(
    team1Player1: joost, team1Player2: richard,
    team2Player1: darius, team2Player2: gavin,
    team1Score: 11, team2Score: 5
)

print("\nResultaten:")
for (player, change) in result1.sorted(by: { $0.key < $1.key }) {
    let sign = change >= 0 ? "+" : ""
    print("\(player): \(sign)\(String(format: "%.2f", change))")
}

print("\nAnalyse WINST (Joost/Richard):")
print("- Richard krijgt MEER punten omdat teamgenoot Joost hogere ELO heeft")
print("- Joost krijgt minder punten omdat teamgenoot Richard lagere ELO heeft")

print("\nAnalyse VERLIES (Darius/Gavin):")
print("- Darius verliest MEER punten omdat hij hogere ELO heeft")
print("- Gavin verliest minder punten omdat hij lagere ELO heeft")

// Test 2: Omgekeerde situatie
print("\n" + String(repeating: "=", count: 50))
print("TEST 2: Darius(1250)/Gavin(1150) vs Joost(1300)/Richard(1100)")
print("Score: 11-7 (Darius/Gavin winnen)")

let result2 = calculateGameResult(
    team1Player1: darius, team1Player2: gavin,
    team2Player1: joost, team2Player2: richard,
    team1Score: 11, team2Score: 7
)

print("\nResultaten:")
for (player, change) in result2.sorted(by: { $0.key < $1.key }) {
    let sign = change >= 0 ? "+" : ""
    print("\(player): \(sign)\(String(format: "%.2f", change))")
}

print("\nAnalyse WINST (Darius/Gavin):")
print("- Gavin krijgt MEER punten omdat teamgenoot Darius hogere ELO heeft")
print("- Darius krijgt minder punten omdat teamgenoot Gavin lagere ELO heeft")

print("\nAnalyse VERLIES (Joost/Richard):")
print("- Joost verliest MEER punten omdat hij hogere ELO heeft")
print("- Richard verliest minder punten omdat hij lagere ELO heeft")

print("\n" + String(repeating: "=", count: 50))
print("CONCLUSIE:")
print("✅ Bij winst: Speler met sterkere teamgenoot wordt meer beloond")
print("✅ Bij verlies: Sterkere speler wordt meer gestraft")
print("✅ Dit stimuleert sterke spelers om goed te presteren")
print("✅ En beloont zwakkere spelers die met sterke partners spelen")

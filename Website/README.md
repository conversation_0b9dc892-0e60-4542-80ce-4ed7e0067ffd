# Table Tennis Beta Testing Landing Page

Een moderne, responsive webpagina voor het distribueren van de Table Tennis iOS app via TestFlight.

## Features

- 🎨 Modern iOS-geïnspireerd design
- 📱 Volledig responsive (werkt op alle apparaten)
- 🌙 Automatische dark mode ondersteuning
- ✨ Subtiele animaties en hover effecten
- 🔍 iOS device detectie
- 📋 Duidelijke installatie-instructies

## Setup

### 1. TestFlight Link Bijwerken

Open `index.html` en vervang `YOUR_TESTFLIGHT_CODE` in de volgende regel:

```html
<a href="https://testflight.apple.com/join/YOUR_TESTFLIGHT_CODE" class="download-btn">
```

Met je echte TestFlight invite code, bijvoorbeeld:
```html
<a href="https://testflight.apple.com/join/abc123def" class="download-btn">
```

### 2. OTA (Over-The-Air) Installatie Setup

Voor directe installatie via Safari browser:

1. **IPA Bestand Voorbereiden:**
   - Exporteer je app als IPA bestand uit Xcode
   - Hernoem het naar `TableTennis.ipa`
   - Upload naar je webserver

2. **Manifest Bestand Configureren:**
   - Open `manifest.plist`
   - Vervang `YOUR_DOMAIN.com` met je echte domain
   - Controleer dat de bundle-identifier klopt (`nl.joostlaurman.tabletennis`)

3. **HTML Link Bijwerken:**
   - Open `index.html`
   - Vervang `YOUR_DOMAIN.com` in de OTA install link

**Vereisten voor OTA installatie:**
- HTTPS hosting (verplicht voor iOS)
- Geldige provisioning profile met geregistreerde device UDIDs
- Correct gesigneerde IPA met developer certificate

### 3. App Icons Voorbereiden

Voor de beste OTA installatie ervaring:

1. **Webpagina Icon (al gedaan):**
   - `app-icon.png` (120x120px) - al gekopieerd uit Xcode

2. **OTA Installatie Icons (optioneel):**
   - `app-icon-57.png` (57x57px) - klein icon tijdens installatie
   - `app-icon-512.png` (512x512px) - groot icon tijdens installatie

**Icons maken uit Xcode app icon:**
```bash
# Resize het bestaande app-icon.png naar verschillende formaten
sips -z 57 57 app-icon.png --out app-icon-57.png
sips -z 512 512 app-icon.png --out app-icon-512.png
```

### 4. Hosting

Upload de bestanden naar een webserver of gebruik een gratis service zoals:

- **GitHub Pages**: Gratis hosting voor statische websites
- **Netlify**: Drag & drop deployment
- **Vercel**: Eenvoudige deployment
- **Firebase Hosting**: Google's hosting platform

**Belangrijk voor IPA bestanden:**
- Zorg dat je webserver grote bestanden (>100MB) ondersteunt
- Sommige gratis hosting services hebben bestandsgrootte limieten
- Overweeg cloud storage (Google Drive, Dropbox) voor grote IPA bestanden

### 4. TestFlight Setup

1. Ga naar [App Store Connect](https://appstoreconnect.apple.com)
2. Selecteer je Table Tennis app
3. Ga naar TestFlight
4. Maak een nieuwe External Testing groep
5. Kopieer de TestFlight invite link
6. Update de link in `index.html`

## Customization

### Kleuren Aanpassen

De hoofdkleuren zijn gedefinieerd in de CSS:

```css
/* Primaire app kleur */
background: #007AFF;

/* Gradient achtergrond */
background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
```

### Features Lijst

Pas de features aan in de HTML:

```html
<div class="features">
    <div class="feature">
        <div class="feature-icon">🏆</div>
        <span>Jouw feature hier</span>
    </div>
</div>
```

### App Informatie

Update de app naam en beschrijving:

```html
<h1>Table Tennis</h1>
<p class="subtitle">Professional Tournament Management</p>
```

## Browser Ondersteuning

- ✅ Safari (iOS/macOS)
- ✅ Chrome
- ✅ Firefox
- ✅ Edge
- ✅ Mobile browsers

## Tips

1. **Test op echte iOS apparaten** om de TestFlight flow te verifiëren
2. **Gebruik HTTPS** voor de beste gebruikerservaring
3. **Monitor TestFlight analytics** om te zien hoeveel testers de app downloaden
4. **Update regelmatig** de landing page met nieuwe features

## Troubleshooting

**TestFlight link werkt niet:**
- Controleer of de invite code correct is
- Zorg dat External Testing is ingeschakeld
- Controleer of er nog plekken beschikbaar zijn (max 10.000 testers)

**OTA installatie werkt niet:**
- Zorg dat je HTTPS gebruikt (HTTP werkt niet voor OTA)
- Controleer of alle URLs in `manifest.plist` correct zijn
- Test alleen in Safari browser op iOS (andere browsers werken niet)
- Controleer of device UDID is geregistreerd in provisioning profile
- Zorg dat IPA correct is gesigneerd met geldige certificate

**"Cannot connect to [domain]" fout:**
- Controleer of `manifest.plist` en `TableTennis.ipa` toegankelijk zijn via HTTPS
- Test de URLs handmatig in browser
- Controleer server configuratie voor .plist en .ipa bestanden

**App installeert maar start niet:**
- Ga naar Settings > General > VPN & Device Management
- Zoek je developer certificate en tap "Trust"
- Controleer of provisioning profile niet is verlopen

**Pagina laadt niet goed:**
- Controleer of alle bestanden correct zijn geüpload
- Zorg dat de webserver HTTPS ondersteunt
- Test in verschillende browsers

**App icon wordt niet getoond:**
- Zorg dat `app-icon.png` in dezelfde map staat als `index.html`
- Controleer of het bestand niet te groot is (max 1MB aanbevolen)
- Gebruik PNG formaat voor de beste kwaliteit

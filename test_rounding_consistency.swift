import Foundation

// Test om te controleren dat beide views nu consistent afronden

print("=== ROUNDING CONSISTENCY TEST ===")
print("<PERSON><PERSON> views moeten nu .rounded() gebruiken")

// Test verschillende ELO change waarden
let testValues: [Double] = [
    -0.7,   // Problematisch geval: Int(-0.7) = 0, maar Int((-0.7).rounded()) = -1
    -0.3,   // Int(-0.3) = 0, maar Int((-0.3).rounded()) = 0
    -0.5,   // Int(-0.5) = 0, maar Int((-0.5).rounded()) = 0 (banker's rounding)
    -0.6,   // Int(-0.6) = 0, maar Int((-0.6).rounded()) = -1
    -1.2,   // Int(-1.2) = -1, Int((-1.2).rounded()) = -1
    -1.7,   // Int(-1.7) = -1, Int((-1.7).rounded()) = -2
    0.0,    // Beide geven 0
    0.3,    // Int(0.3) = 0, Int(0.3.rounded()) = 0
    0.5,    // Int(0.5) = 0, Int(0.5.rounded()) = 0 (banker's rounding)
    0.6,    // Int(0.6) = 0, Int(0.6.rounded()) = 1
    0.7,    // Int(0.7) = 0, Int(0.7.rounded()) = 1
    1.2,    // Int(1.2) = 1, Int(1.2.rounded()) = 1
    1.7     // Int(1.7) = 1, Int(1.7.rounded()) = 2
]

// Simuleer oude PlayerDetailView methode (truncate)
func oldPlayerDetailViewFormat(_ value: Double) -> String {
    return "\(value >= 0 ? "+" : "")\(Int(value))"
}

// Simuleer nieuwe PlayerDetailView methode (rounded)
func newPlayerDetailViewFormat(_ value: Double) -> String {
    return "\(value >= 0 ? "+" : "")\(Int(value.rounded()))"
}

// Simuleer MatchDetailView methode (altijd al rounded)
func matchDetailViewFormat(_ value: Double) -> String {
    if value > 0 {
        return "+\(Int(value.rounded()))"
    } else if value < 0 {
        return "\(Int(value.rounded()))"
    } else {
        return "0"
    }
}

print("\n" + String(repeating: "=", count: 80))
print("Value\t\tOld Player\tNew Player\tMatch Detail\tConsistent?")
print(String(repeating: "=", count: 80))

for value in testValues {
    let oldPlayer = oldPlayerDetailViewFormat(value)
    let newPlayer = newPlayerDetailViewFormat(value)
    let matchDetail = matchDetailViewFormat(value)
    
    let consistent = (newPlayer == matchDetail) ? "✅ YES" : "❌ NO"
    let oldConsistent = (oldPlayer == matchDetail) ? "✅" : "❌"
    
    print(String(format: "%.1f\t\t%@\t\t%@\t\t%@\t\t%@", 
                 value, oldPlayer, newPlayer, matchDetail, consistent))
}

print(String(repeating: "=", count: 80))

// Specifiek test het problematische geval
print("\nSPECIFIEK TEST: -0.7 (jouw voorbeeld)")
let problematicValue = -0.7
print("Oude PlayerDetailView: \(oldPlayerDetailViewFormat(problematicValue))")
print("Nieuwe PlayerDetailView: \(newPlayerDetailViewFormat(problematicValue))")
print("MatchDetailView: \(matchDetailViewFormat(problematicValue))")

let isFixed = newPlayerDetailViewFormat(problematicValue) == matchDetailViewFormat(problematicValue)
print("Probleem opgelost: \(isFixed ? "✅ JA" : "❌ NEE")")

print("\n" + String(repeating: "=", count: 80))
print("CONCLUSIE:")
print("✅ Beide views gebruiken nu Int(value.rounded())")
print("✅ Consistente afronding gegarandeerd")
print("✅ Geen meer -0 vs -1 verschillen")

print("\nNOTE: Swift's .rounded() gebruikt 'banker's rounding':")
print("- 0.5 → 0 (naar even getal)")
print("- 1.5 → 2 (naar even getal)")
print("- -0.5 → 0 (naar even getal)")
print("- -1.5 → -2 (naar even getal)")

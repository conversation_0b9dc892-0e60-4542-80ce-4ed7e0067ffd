import Foundation

// Test om te valideren dat de nieuwe score ratio implementatie correct werkt
// Vergelijkt win/loss vs score ratio resultaten

struct TestPlayer {
    let name: String
    var eloRating: Double
}

struct EloCalculator {
    static let defaultKFactor: Double = 32.0
    
    static func expectedScore(playerRating: Double, opponentRating: Double) -> Double {
        let ratingDifference = opponentRating - playerRating
        return 1.0 / (1.0 + pow(10.0, ratingDifference / 400.0))
    }
    
    static func ratingChange(currentRating: Double,
                           opponentRating: Double,
                           actualScore: Double,
                           kFactor: Double = defaultKFactor) -> Double {
        let expectedScore = expectedScore(playerRating: currentRating, opponentRating: opponentRating)
        return kFactor * (actualScore - expectedScore)
    }
}

// Test scenario: 4 spelers met gelijke ELO (1200)
let joost = TestPlayer(name: "Joost", eloRating: 1200)
let richard = TestPlayer(name: "<PERSON>", eloRating: 1200)
let darius = TestPlayer(name: "<PERSON>", eloRating: 1200)
let gavin = TestPlayer(name: "<PERSON>", eloRating: 1200)

print("=== SCORE RATIO VALIDATION TEST ===")
print("Initial ELO: All players = 1200")

// Test verschillende scores om het verschil te laten zien
let testScores = [
    (team1: 11, team2: 0, description: "Dominant win"),
    (team1: 11, team2: 5, description: "Clear win"),
    (team1: 11, team2: 8, description: "Close win"),
    (team1: 11, team2: 9, description: "Very close win"),
    (team1: 11, team2: 10, description: "Narrow win")
]

func calculateTeamEloChange(team1Score: Int, team2Score: Int, useScoreRatio: Bool) -> (team1Change: Double, team2Change: Double) {
    let totalPoints = team1Score + team2Score
    
    let actualScore1: Double
    let actualScore2: Double
    
    if useScoreRatio {
        actualScore1 = Double(team1Score) / Double(totalPoints)
        actualScore2 = Double(team2Score) / Double(totalPoints)
    } else {
        // Win/loss approach
        actualScore1 = team1Score > team2Score ? 1.0 : 0.0
        actualScore2 = team2Score > team1Score ? 1.0 : 0.0
    }
    
    // Beide teams hebben gelijke ELO, dus expected score = 0.5
    let team1Change = EloCalculator.ratingChange(
        currentRating: 1200.0,
        opponentRating: 1200.0,
        actualScore: actualScore1
    )
    
    let team2Change = EloCalculator.ratingChange(
        currentRating: 1200.0,
        opponentRating: 1200.0,
        actualScore: actualScore2
    )
    
    return (team1Change, team2Change)
}

print("\n" + String(repeating: "-", count: 80))
print("Score\t\tDescription\t\tWin/Loss\t\tScore Ratio")
print(String(repeating: "-", count: 80))

for testScore in testScores {
    let winLoss = calculateTeamEloChange(team1Score: testScore.team1, team2Score: testScore.team2, useScoreRatio: false)
    let scoreRatio = calculateTeamEloChange(team1Score: testScore.team1, team2Score: testScore.team2, useScoreRatio: true)
    
    let scoreText = "\(testScore.team1)-\(testScore.team2)"
    let winLossText = String(format: "%.1f/%.1f", winLoss.team1Change, winLoss.team2Change)
    let scoreRatioText = String(format: "%.1f/%.1f", scoreRatio.team1Change, scoreRatio.team2Change)
    
    print("\(scoreText)\t\t\(testScore.description)\t\t\(winLossText)\t\t\(scoreRatioText)")
}

print(String(repeating: "-", count: 80))

// Demonstreer teammate ratio verdeling
print("\n=== TEAMMATE RATIO DISTRIBUTION EXAMPLE ===")
print("Game: Team A (Joost 1300 + Richard 1100) vs Team B (Darius 1200 + Gavin 1200)")
print("Score: 11-7 (Team A wins)")

let joostHigh = TestPlayer(name: "Joost", eloRating: 1300)
let richardLow = TestPlayer(name: "Richard", eloRating: 1100)
let dariusMid = TestPlayer(name: "Darius", eloRating: 1200)
let gavinMid = TestPlayer(name: "Gavin", eloRating: 1200)

// Team averages
let teamAAverage = (joostHigh.eloRating + richardLow.eloRating) / 2.0  // 1200
let teamBAverage = (dariusMid.eloRating + gavinMid.eloRating) / 2.0    // 1200

// Score ratio
let totalPoints = 11 + 7
let teamAScoreRatio = Double(11) / Double(totalPoints)  // 0.611
let teamBScoreRatio = Double(7) / Double(totalPoints)   // 0.389

// Team ELO changes
let teamAChange = EloCalculator.ratingChange(
    currentRating: teamAAverage,
    opponentRating: teamBAverage,
    actualScore: teamAScoreRatio,
    kFactor: EloCalculator.defaultKFactor * 0.75
)

let teamBChange = EloCalculator.ratingChange(
    currentRating: teamBAverage,
    opponentRating: teamAAverage,
    actualScore: teamBScoreRatio,
    kFactor: EloCalculator.defaultKFactor * 0.75
)

print("Team A total change: \(teamAChange)")
print("Team B total change: \(teamBChange)")

// Distribute based on teammate ELO
let teamATotalElo = joostHigh.eloRating + richardLow.eloRating
let joostShare = richardLow.eloRating / teamATotalElo  // Based on Richard's ELO
let richardShare = joostHigh.eloRating / teamATotalElo // Based on Joost's ELO

let joostFinalChange = teamAChange * joostShare
let richardFinalChange = teamAChange * richardShare

// Voor verliezende team: verdeel gebaseerd op eigen ELO (hogere ELO verliest meer)
let teamBTotalElo = dariusMid.eloRating + gavinMid.eloRating
let dariusLossShare = dariusMid.eloRating / teamBTotalElo   // Based on own ELO
let gavinLossShare = gavinMid.eloRating / teamBTotalElo     // Based on own ELO

let dariusFinalChange = teamBChange * dariusLossShare
let gavinFinalChange = teamBChange * gavinLossShare

print("\nFinal individual changes:")
print("Joost (1300, teammate 1100): \(joostFinalChange) (\(joostShare * 100)%)")
print("Richard (1100, teammate 1300): \(richardFinalChange) (\(richardShare * 100)%)")
print("Darius (1200, teammate 1200): \(dariusFinalChange) (\(dariusShare * 100)%)")
print("Gavin (1200, teammate 1200): \(gavinFinalChange) (\(gavinShare * 100)%)")

print("\nNote: Richard gets more ELO despite having lower rating because his teammate has higher ELO!")
print("This rewards players who perform well with stronger partners.")

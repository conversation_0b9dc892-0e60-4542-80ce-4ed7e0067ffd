import Foundation

// Test Mix & Match ELO berekeningen
// Scenario: 4 spelers met gel<PERSON><PERSON><PERSON> (1200)
// Game 1: <PERSON><PERSON>/<PERSON> vs <PERSON>/<PERSON> (11-0)
// Game 2: <PERSON>/<PERSON> vs <PERSON>/<PERSON> (0-11)
// Game 3: <PERSON>/<PERSON> vs <PERSON>/<PERSON> (0-11)
//
// NEW: Test with score ratio instead of win/loss
// Game 1: Jo<PERSON>/<PERSON> vs <PERSON>/<PERSON> (11-5) - actualScore 11/16=0.6875 vs 5/16=0.3125
// Game 2: <PERSON>/<PERSON> vs <PERSON>/<PERSON> (6-11) - actualScore 6/17=0.353 vs 11/17=0.647
// Game 3: <PERSON>/<PERSON> vs <PERSON>/<PERSON> (8-11) - actualScore 8/19=0.421 vs 11/19=0.579

struct TestPlayer {
    let name: String
    var eloRating: Double
}

struct EloCalculator {
    static let defaultKFactor: Double = 32.0
    
    static func expectedScore(playerRating: Double, opponentRating: Double) -> Double {
        let ratingDifference = opponentRating - playerRating
        return 1.0 / (1.0 + pow(10.0, ratingDifference / 400.0))
    }
    
    static func ratingChange(currentRating: Double,
                           opponentRating: Double,
                           actualScore: Double,
                           kFactor: Double = defaultKFactor) -> Double {
        let expectedScore = expectedScore(playerRating: currentRating, opponentRating: opponentRating)
        return kFactor * (actualScore - expectedScore)
    }
}

// Initialize players with ELO 1200
let joostOriginal = TestPlayer(name: "Joost", eloRating: 1200)
let richardOriginal = TestPlayer(name: "Richard", eloRating: 1200)
let dariusOriginal = TestPlayer(name: "Darius", eloRating: 1200)
let gavinOriginal = TestPlayer(name: "Gavin", eloRating: 1200)

print("=== INITIAL RATINGS ===")
print("Joost: \(joostOriginal.eloRating)")
print("Richard: \(richardOriginal.eloRating)")
print("Darius: \(dariusOriginal.eloRating)")
print("Gavin: \(gavinOriginal.eloRating)")

// STATISCHE ELO BEREKENING - alle games gebruiken oorspronkelijke ratings
var totalChanges: [String: Double] = [
    "Joost": 0.0,
    "Richard": 0.0,
    "Darius": 0.0,
    "Gavin": 0.0
]

// Game 1: Joost/Richard (Team 1) vs Darius/Gavin (Team 2) - Score: 11-0
print("\n=== GAME 1: Joost/Richard vs Darius/Gavin (11-0) ===")
let team1AvgGame1 = (joostOriginal.eloRating + richardOriginal.eloRating) / 2.0
let team2AvgGame1 = (dariusOriginal.eloRating + gavinOriginal.eloRating) / 2.0

// Winners (Joost, Richard): actualScore = 1.0
let joostChange1 = EloCalculator.ratingChange(
    currentRating: joostOriginal.eloRating,
    opponentRating: team2AvgGame1,
    actualScore: 1.0
)

let richardChange1 = EloCalculator.ratingChange(
    currentRating: richardOriginal.eloRating,
    opponentRating: team2AvgGame1,
    actualScore: 1.0
)

// Losers (Darius, Gavin): actualScore = 0.0
let dariusChange1 = EloCalculator.ratingChange(
    currentRating: dariusOriginal.eloRating,
    opponentRating: team1AvgGame1,
    actualScore: 0.0
)

let gavinChange1 = EloCalculator.ratingChange(
    currentRating: gavinOriginal.eloRating,
    opponentRating: team1AvgGame1,
    actualScore: 0.0
)

print("Joost change: \(joostChange1)")
print("Richard change: \(richardChange1)")
print("Darius change: \(dariusChange1)")
print("Gavin change: \(gavinChange1)")

totalChanges["Joost"]! += joostChange1
totalChanges["Richard"]! += richardChange1
totalChanges["Darius"]! += dariusChange1
totalChanges["Gavin"]! += gavinChange1

// Game 2: Darius/Richard (Team 1) vs Gavin/Joost (Team 2) - Score: 0-11
print("\n=== GAME 2: Darius/Richard vs Gavin/Joost (0-11) ===")
// Gebruik OORSPRONKELIJKE ratings voor team gemiddeldes
let team1AvgGame2 = (dariusOriginal.eloRating + richardOriginal.eloRating) / 2.0
let team2AvgGame2 = (gavinOriginal.eloRating + joostOriginal.eloRating) / 2.0

// Losers (Darius, Richard): actualScore = 0.0
let dariusChange2 = EloCalculator.ratingChange(
    currentRating: dariusOriginal.eloRating,
    opponentRating: team2AvgGame2,
    actualScore: 0.0
)

let richardChange2 = EloCalculator.ratingChange(
    currentRating: richardOriginal.eloRating,
    opponentRating: team2AvgGame2,
    actualScore: 0.0
)

// Winners (Gavin, Joost): actualScore = 1.0
let gavinChange2 = EloCalculator.ratingChange(
    currentRating: gavinOriginal.eloRating,
    opponentRating: team1AvgGame2,
    actualScore: 1.0
)

let joostChange2 = EloCalculator.ratingChange(
    currentRating: joostOriginal.eloRating,
    opponentRating: team1AvgGame2,
    actualScore: 1.0
)

print("Darius change: \(dariusChange2)")
print("Richard change: \(richardChange2)")
print("Gavin change: \(gavinChange2)")
print("Joost change: \(joostChange2)")

totalChanges["Joost"]! += joostChange2
totalChanges["Richard"]! += richardChange2
totalChanges["Darius"]! += dariusChange2
totalChanges["Gavin"]! += gavinChange2

// Game 3: Gavin/Richard (Team 1) vs Darius/Joost (Team 2) - Score: 0-11
print("\n=== GAME 3: Gavin/Richard vs Darius/Joost (0-11) ===")
// Gebruik OORSPRONKELIJKE ratings voor team gemiddeldes
let team1AvgGame3 = (gavinOriginal.eloRating + richardOriginal.eloRating) / 2.0
let team2AvgGame3 = (dariusOriginal.eloRating + joostOriginal.eloRating) / 2.0

// Losers (Gavin, Richard): actualScore = 0.0
let gavinChange3 = EloCalculator.ratingChange(
    currentRating: gavinOriginal.eloRating,
    opponentRating: team2AvgGame3,
    actualScore: 0.0
)

let richardChange3 = EloCalculator.ratingChange(
    currentRating: richardOriginal.eloRating,
    opponentRating: team2AvgGame3,
    actualScore: 0.0
)

// Winners (Darius, Joost): actualScore = 1.0
let dariusChange3 = EloCalculator.ratingChange(
    currentRating: dariusOriginal.eloRating,
    opponentRating: team1AvgGame3,
    actualScore: 1.0 
)

let joostChange3 = EloCalculator.ratingChange(
    currentRating: joostOriginal.eloRating,
    opponentRating: team1AvgGame3,
    actualScore: 1.0
)

print("Gavin change: \(gavinChange3)")
print("Richard change: \(richardChange3)")
print("Darius change: \(dariusChange3)")
print("Joost change: \(joostChange3)")

totalChanges["Joost"]! += joostChange3
totalChanges["Richard"]! += richardChange3
totalChanges["Darius"]! += dariusChange3
totalChanges["Gavin"]! += gavinChange3

print("\n=== TOTAL CHANGES (STATIC ELO) ===")
print("Joost total: \(totalChanges["Joost"]!)")
print("Richard total: \(totalChanges["Richard"]!)")
print("Darius total: \(totalChanges["Darius"]!)")
print("Gavin total: \(totalChanges["Gavin"]!)")

print("\n=== FINAL RATINGS ===")
print("Joost: \(joostOriginal.eloRating + totalChanges["Joost"]!)")
print("Richard: \(richardOriginal.eloRating + totalChanges["Richard"]!)")
print("Darius: \(dariusOriginal.eloRating + totalChanges["Darius"]!)")
print("Gavin: \(gavinOriginal.eloRating + totalChanges["Gavin"]!)")

print("\n=== EXPECTED vs ACTUAL ===")
print("Expected (with /2 for team): Joost +18, Richard -6, Darius -6, Gavin -6")
print("Actual (full K-factor): Joost \(totalChanges["Joost"]!), Richard \(totalChanges["Richard"]!), Darius \(totalChanges["Darius"]!), Gavin \(totalChanges["Gavin"]!)")

print("\n=== TESTING WITH TEAMMATE ELO RATIO DISTRIBUTION ===")
// Test met verdeling naar rato van teamgenoot ELO
var totalChangesRatio: [String: Double] = [
    "Joost": 0.0,
    "Richard": 0.0,
    "Darius": 0.0,
    "Gavin": 0.0
]

func distributeEloChange(player1Name: String, player1Elo: Double, player1Change: Double,
                        player2Name: String, player2Elo: Double, player2Change: Double) -> (Double, Double) {
    // Verdeling gebaseerd op teamgenoot ELO
    // Speler met hogere ELO teamgenoot krijgt groter deel
    let totalTeammateElo = player1Elo + player2Elo
    let player1Share = player2Elo / totalTeammateElo // Gebaseerd op teamgenoot (player2) ELO
    let player2Share = player1Elo / totalTeammateElo // Gebaseerd op teamgenoot (player1) ELO

    // Beide spelers hebben dezelfde totale wijziging (omdat ze gelijke ELO hebben)
    let totalChange = player1Change // of player2Change, beide zijn gelijk

    return (totalChange * player1Share, totalChange * player2Share)
}

// Game 1: Joost/Richard vs Darius/Gavin (11-0)
print("Game 1 distribution:")
let (joostChange1Ratio, richardChange1Ratio) = distributeEloChange(
    player1Name: "Joost", player1Elo: joostOriginal.eloRating, player1Change: joostChange1,
    player2Name: "Richard", player2Elo: richardOriginal.eloRating, player2Change: richardChange1
)
let (dariusChange1Ratio, gavinChange1Ratio) = distributeEloChange(
    player1Name: "Darius", player1Elo: dariusOriginal.eloRating, player1Change: dariusChange1,
    player2Name: "Gavin", player2Elo: gavinOriginal.eloRating, player2Change: gavinChange1
)

print("  Joost: \(joostChange1Ratio), Richard: \(richardChange1Ratio)")
print("  Darius: \(dariusChange1Ratio), Gavin: \(gavinChange1Ratio)")

totalChangesRatio["Joost"]! += joostChange1Ratio
totalChangesRatio["Richard"]! += richardChange1Ratio
totalChangesRatio["Darius"]! += dariusChange1Ratio
totalChangesRatio["Gavin"]! += gavinChange1Ratio

// Game 2: Darius/Richard vs Gavin/Joost (0-11)
print("Game 2 distribution:")
let (dariusChange2Ratio, richardChange2Ratio) = distributeEloChange(
    player1Name: "Darius", player1Elo: dariusOriginal.eloRating, player1Change: dariusChange2,
    player2Name: "Richard", player2Elo: richardOriginal.eloRating, player2Change: richardChange2
)
let (gavinChange2Ratio, joostChange2Ratio) = distributeEloChange(
    player1Name: "Gavin", player1Elo: gavinOriginal.eloRating, player1Change: gavinChange2,
    player2Name: "Joost", player2Elo: joostOriginal.eloRating, player2Change: joostChange2
)

print("  Darius: \(dariusChange2Ratio), Richard: \(richardChange2Ratio)")
print("  Gavin: \(gavinChange2Ratio), Joost: \(joostChange2Ratio)")

totalChangesRatio["Joost"]! += joostChange2Ratio
totalChangesRatio["Richard"]! += richardChange2Ratio
totalChangesRatio["Darius"]! += dariusChange2Ratio
totalChangesRatio["Gavin"]! += gavinChange2Ratio

// Game 3: Gavin/Richard vs Darius/Joost (0-11)
print("Game 3 distribution:")
let (gavinChange3Ratio, richardChange3Ratio) = distributeEloChange(
    player1Name: "Gavin", player1Elo: gavinOriginal.eloRating, player1Change: gavinChange3,
    player2Name: "Richard", player2Elo: richardOriginal.eloRating, player2Change: richardChange3
)
let (dariusChange3Ratio, joostChange3Ratio) = distributeEloChange(
    player1Name: "Darius", player1Elo: dariusOriginal.eloRating, player1Change: dariusChange3,
    player2Name: "Joost", player2Elo: joostOriginal.eloRating, player2Change: joostChange3
)

print("  Gavin: \(gavinChange3Ratio), Richard: \(richardChange3Ratio)")
print("  Darius: \(dariusChange3Ratio), Joost: \(joostChange3Ratio)")

totalChangesRatio["Joost"]! += joostChange3Ratio
totalChangesRatio["Richard"]! += richardChange3Ratio
totalChangesRatio["Darius"]! += dariusChange3Ratio
totalChangesRatio["Gavin"]! += gavinChange3Ratio

print("\n=== TOTAL CHANGES WITH TEAMMATE ELO RATIO ===")
print("Joost: \(totalChangesRatio["Joost"]!)")
print("Richard: \(totalChangesRatio["Richard"]!)")
print("Darius: \(totalChangesRatio["Darius"]!)")
print("Gavin: \(totalChangesRatio["Gavin"]!)")

print("\n=== COMPARISON ===")
print("Simple /2 division: Joost 18.0, Richard -6.0, Darius -6.0, Gavin -6.0")
print("Teammate ELO ratio: Joost \(totalChangesRatio["Joost"]!), Richard \(totalChangesRatio["Richard"]!), Darius \(totalChangesRatio["Darius"]!), Gavin \(totalChangesRatio["Gavin"]!)")

print("\n=== TESTING WITH DIFFERENT ELO RATINGS ===")
// Test met verschillende ELO ratings om het verschil te laten zien
let joostDiff = TestPlayer(name: "Joost", eloRating: 1300) // Hoog
let richardDiff = TestPlayer(name: "Richard", eloRating: 1100) // Laag
let dariusDiff = TestPlayer(name: "Darius", eloRating: 1250) // Middel
let gavinDiff = TestPlayer(name: "Gavin", eloRating: 1150) // Middel-laag

print("Different ELO ratings: Joost=1300, Richard=1100, Darius=1250, Gavin=1150")

// Simuleer Game 1: Joost(1300)/Richard(1100) vs Darius(1250)/Gavin(1150) - score 11-0
// Team 1 gemiddelde: (1300+1100)/2 = 1200
// Team 2 gemiddelde: (1250+1150)/2 = 1200
// Dus expected score = 0.5, wijziging = 24 * 0.5 = 12 per speler

let gameChangeExample = 12.0 // Voorbeeld wijziging

// Verdeling voor winnende team (Joost/Richard)
let totalTeammateEloWin = joostDiff.eloRating + richardDiff.eloRating // 2400
let joostShareWin = richardDiff.eloRating / totalTeammateEloWin // 1100/2400 = 45.8%
let richardShareWin = joostDiff.eloRating / totalTeammateEloWin // 1300/2400 = 54.2%

let joostChangeExample = gameChangeExample * joostShareWin
let richardChangeExample = gameChangeExample * richardShareWin

print("Winning team distribution (total +\(gameChangeExample)):")
print("  Joost (1300 ELO, teammate 1100): \(joostChangeExample) (\(joostShareWin * 100)%)")
print("  Richard (1100 ELO, teammate 1300): \(richardChangeExample) (\(richardShareWin * 100)%)")

// Verdeling voor verliezende team (Darius/Gavin)
let totalTeammateEloLose = dariusDiff.eloRating + gavinDiff.eloRating // 2400
let dariusShareLose = gavinDiff.eloRating / totalTeammateEloLose // 1150/2400 = 47.9%
let gavinShareLose = dariusDiff.eloRating / totalTeammateEloLose // 1250/2400 = 52.1%

let dariusChangeExample = -gameChangeExample * dariusShareLose
let gavinChangeExample = -gameChangeExample * gavinShareLose

print("Losing team distribution (total -\(gameChangeExample)):")
print("  Darius (1250 ELO, teammate 1150): \(dariusChangeExample) (\(dariusShareLose * 100)%)")
print("  Gavin (1150 ELO, teammate 1250): \(gavinChangeExample) (\(gavinShareLose * 100)%)")

print("\nNote: Richard (lowest ELO) gets the MOST points because his teammate has the HIGHEST ELO!")

print("\n" + String(repeating: "=", count: 60))
print("=== TESTING WITH SCORE RATIO (NEW APPROACH) ===")
print(String(repeating: "=", count: 60))

// Test met score ratio in plaats van win/loss
// Game 1: 11-5, Game 2: 6-11, Game 3: 8-11

func calculateScoreRatioChange(player1Name: String, player1Elo: Double,
                              player2Name: String, player2Elo: Double,
                              team1Score: Int, team2Score: Int,
                              playerInTeam1: Bool) -> (Double, Double) {
    let totalPoints = team1Score + team2Score
    let team1ScoreRatio = Double(team1Score) / Double(totalPoints)
    let team2ScoreRatio = Double(team2Score) / Double(totalPoints)

    // Bereken team gemiddelde ELO
    let teamAverageElo = (player1Elo + player2Elo) / 2.0
    let opponentAverageElo = teamAverageElo // Beide teams hebben zelfde gemiddelde in dit scenario

    // Bereken ELO wijziging voor elk team
    let team1Change = EloCalculator.ratingChange(
        currentRating: teamAverageElo,
        opponentRating: opponentAverageElo,
        actualScore: team1ScoreRatio,
        kFactor: EloCalculator.defaultKFactor 
    )

    let team2Change = EloCalculator.ratingChange(
        currentRating: teamAverageElo,
        opponentRating: opponentAverageElo,
        actualScore: team2ScoreRatio,
        kFactor: EloCalculator.defaultKFactor 
    )

    // Verdeel naar rato van teammate ELO
    let totalTeammateElo = player1Elo + player2Elo
    let player1Share = player2Elo / totalTeammateElo // Gebaseerd op teammate ELO
    let player2Share = player1Elo / totalTeammateElo

    let player1FinalChange = (playerInTeam1 ? team1Change : team2Change) * player1Share
    let player2FinalChange = (playerInTeam1 ? team1Change : team2Change) * player2Share

    return (player1FinalChange, player2FinalChange)
}

var totalChangesScoreRatio: [String: Double] = [
    "Joost": 0.0,
    "Richard": 0.0,
    "Darius": 0.0,
    "Gavin": 0.0
]

// Game 1: Joost/Richard vs Darius/Gavin (11-5)
print("Game 1: Joost/Richard vs Darius/Gavin (11-5)")
let totalPoints1 = 11 + 5
let team1Ratio1 = Double(11) / Double(totalPoints1) // 0.6875
let team2Ratio1 = Double(5) / Double(totalPoints1)  // 0.3125
print("  Score ratios: Team1=\(team1Ratio1), Team2=\(team2Ratio1)")

let (joostChange1SR, richardChange1SR) = calculateScoreRatioChange(
    player1Name: "Joost", player1Elo: joostOriginal.eloRating,
    player2Name: "Richard", player2Elo: richardOriginal.eloRating,
    team1Score: 11, team2Score: 5, playerInTeam1: true
)

let (dariusChange1SR, gavinChange1SR) = calculateScoreRatioChange(
    player1Name: "Darius", player1Elo: dariusOriginal.eloRating,
    player2Name: "Gavin", player2Elo: gavinOriginal.eloRating,
    team1Score: 11, team2Score: 5, playerInTeam1: false
)

print("  Joost: \(joostChange1SR), Richard: \(richardChange1SR)")
print("  Darius: \(dariusChange1SR), Gavin: \(gavinChange1SR)")

totalChangesScoreRatio["Joost"]! += joostChange1SR
totalChangesScoreRatio["Richard"]! += richardChange1SR
totalChangesScoreRatio["Darius"]! += dariusChange1SR
totalChangesScoreRatio["Gavin"]! += gavinChange1SR

// Game 2: Darius/Richard vs Gavin/Joost (6-11)
print("\nGame 2: Darius/Richard vs Gavin/Joost (6-11)")
let totalPoints2 = 6 + 11
let team1Ratio2 = Double(6) / Double(totalPoints2)  // 0.353
let team2Ratio2 = Double(11) / Double(totalPoints2) // 0.647
print("  Score ratios: Team1=\(team1Ratio2), Team2=\(team2Ratio2)")

let (dariusChange2SR, richardChange2SR) = calculateScoreRatioChange(
    player1Name: "Darius", player1Elo: dariusOriginal.eloRating,
    player2Name: "Richard", player2Elo: richardOriginal.eloRating,
    team1Score: 6, team2Score: 11, playerInTeam1: true
)

let (gavinChange2SR, joostChange2SR) = calculateScoreRatioChange(
    player1Name: "Gavin", player1Elo: gavinOriginal.eloRating,
    player2Name: "Joost", player2Elo: joostOriginal.eloRating,
    team1Score: 6, team2Score: 11, playerInTeam1: false
)

print("  Darius: \(dariusChange2SR), Richard: \(richardChange2SR)")
print("  Gavin: \(gavinChange2SR), Joost: \(joostChange2SR)")

totalChangesScoreRatio["Joost"]! += joostChange2SR
totalChangesScoreRatio["Richard"]! += richardChange2SR
totalChangesScoreRatio["Darius"]! += dariusChange2SR
totalChangesScoreRatio["Gavin"]! += gavinChange2SR

// Game 3: Gavin/Richard vs Darius/Joost (8-11)
print("\nGame 3: Gavin/Richard vs Darius/Joost (8-11)")
let totalPoints3 = 8 + 11
let team1Ratio3 = Double(8) / Double(totalPoints3)  // 0.421
let team2Ratio3 = Double(11) / Double(totalPoints3) // 0.579
print("  Score ratios: Team1=\(team1Ratio3), Team2=\(team2Ratio3)")

let (gavinChange3SR, richardChange3SR) = calculateScoreRatioChange(
    player1Name: "Gavin", player1Elo: gavinOriginal.eloRating,
    player2Name: "Richard", player2Elo: richardOriginal.eloRating,
    team1Score: 8, team2Score: 11, playerInTeam1: true
)

let (dariusChange3SR, joostChange3SR) = calculateScoreRatioChange(
    player1Name: "Darius", player1Elo: dariusOriginal.eloRating,
    player2Name: "Joost", player2Elo: joostOriginal.eloRating,
    team1Score: 8, team2Score: 11, playerInTeam1: false
)

print("  Gavin: \(gavinChange3SR), Richard: \(richardChange3SR)")
print("  Darius: \(dariusChange3SR), Joost: \(joostChange3SR)")

totalChangesScoreRatio["Joost"]! += joostChange3SR
totalChangesScoreRatio["Richard"]! += richardChange3SR
totalChangesScoreRatio["Darius"]! += dariusChange3SR
totalChangesScoreRatio["Gavin"]! += gavinChange3SR

print("\n=== TOTAL CHANGES WITH SCORE RATIO ===")
print("Joost: \(totalChangesScoreRatio["Joost"]!)")
print("Richard: \(totalChangesScoreRatio["Richard"]!)")
print("Darius: \(totalChangesScoreRatio["Darius"]!)")
print("Gavin: \(totalChangesScoreRatio["Gavin"]!)")

print("\n=== FINAL COMPARISON ===")
print("Win/Loss (11-0): Joost +18, Richard -6, Darius -6, Gavin -6")
print("Score Ratio: Joost \(totalChangesScoreRatio["Joost"]!), Richard \(totalChangesScoreRatio["Richard"]!), Darius \(totalChangesScoreRatio["Darius"]!), Gavin \(totalChangesScoreRatio["Gavin"]!)")
print("\nScore ratio gives more nuanced results based on actual game performance!")

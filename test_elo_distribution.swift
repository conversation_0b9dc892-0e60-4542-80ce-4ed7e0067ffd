import Foundation

// Test om te valideren dat ELO changes correct worden verdeeld
// naar rato van speler ELO ratings

print("=== ELO DISTRIBUTION TEST ===")
print("ELO changes moeten correct worden verdeeld tussen teamgenoten")

// Simuleer de nieuwe logica
func calculateEloShares(player1Elo: Double, player2Elo: Double, isWin: Bool) -> (player1Share: Double, player2Share: Double) {
    if isWin {
        // WINST: S<PERSON>er met lagere ELO krijgt meer punten (inverse verdeling)
        let maxElo = max(player1Elo, player2Elo)
        
        // Inverse shares: lagere ELO = hogere share
        let player1InverseElo = maxElo - player1Elo + maxElo
        let player2InverseElo = maxElo - player2Elo + maxElo
        let totalInverseElo = player1InverseElo + player2InverseElo
        
        let player1Share = player1InverseElo / totalInverseElo
        let player2Share = player2InverseElo / totalInverseElo
        
        return (player1Share, player2Share)
    } else {
        // VERLIES: <PERSON><PERSON><PERSON> met hogere ELO verliest meer punten (normale verdeling)
        let totalElo = player1Elo + player2Elo
        let player1Share = player1Elo / totalElo
        let player2Share = player2Elo / totalElo
        
        return (player1Share, player2Share)
    }
}

// Test data gebaseerd op jouw voorbeeld
let joostElo = 1224.0
let dariusElo = 1192.0
let gavinElo = 1192.0
let richardElo = 1192.0

print("\n" + String(repeating: "=", count: 60))
print("STARTING ELO RATINGS:")
print("Joost: \(Int(joostElo))")
print("Darius: \(Int(dariusElo))")
print("Gavin: \(Int(gavinElo))")
print("Richard: \(Int(richardElo))")

print("\n" + String(repeating: "=", count: 60))
print("GAME #1: Gavin/Joost vs Richard/Darius: 11-0")
print("Team 1 (WINNERS): Gavin (\(Int(gavinElo))) + Joost (\(Int(joostElo)))")
print("Team 2 (LOSERS): Richard (\(Int(richardElo))) + Darius (\(Int(dariusElo)))")

// Test winst verdeling (Team 1)
let team1Shares = calculateEloShares(player1Elo: gavinElo, player2Elo: joostElo, isWin: true)
print("\nWINST VERDELING (Team 1):")
print("Gavin share: \(String(format: "%.3f", team1Shares.player1Share)) (\(String(format: "%.1f", team1Shares.player1Share * 100))%)")
print("Joost share: \(String(format: "%.3f", team1Shares.player2Share)) (\(String(format: "%.1f", team1Shares.player2Share * 100))%)")

// Test verlies verdeling (Team 2)
let team2Shares = calculateEloShares(player1Elo: richardElo, player2Elo: dariusElo, isWin: false)
print("\nVERLIES VERDELING (Team 2):")
print("Richard share: \(String(format: "%.3f", team2Shares.player1Share)) (\(String(format: "%.1f", team2Shares.player1Share * 100))%)")
print("Darius share: \(String(format: "%.3f", team2Shares.player2Share)) (\(String(format: "%.1f", team2Shares.player2Share * 100))%)")

// Simuleer team ELO change van +16 punten
let teamEloChange = 16.0
print("\nSIMULATIE MET +\(Int(teamEloChange)) TEAM ELO CHANGE:")
print("Gavin krijgt: +\(String(format: "%.1f", teamEloChange * team1Shares.player1Share)) punten")
print("Joost krijgt: +\(String(format: "%.1f", teamEloChange * team1Shares.player2Share)) punten")
print("Richard verliest: \(String(format: "%.1f", -teamEloChange * team2Shares.player1Share)) punten")
print("Darius verliest: \(String(format: "%.1f", -teamEloChange * team2Shares.player2Share)) punten")

print("\n" + String(repeating: "=", count: 60))
print("LOGICA VALIDATIE:")

// Test verschillende ELO combinaties
let testCases = [
    ("Gelijke ELO", 1200.0, 1200.0),
    ("Kleine verschillen", 1200.0, 1220.0),
    ("Grote verschillen", 1200.0, 1400.0),
    ("Jouw voorbeeld", gavinElo, joostElo)
]

for (description, elo1, elo2) in testCases {
    let winShares = calculateEloShares(player1Elo: elo1, player2Elo: elo2, isWin: true)
    let lossShares = calculateEloShares(player1Elo: elo1, player2Elo: elo2, isWin: false)
    
    print("\n\(description): \(Int(elo1)) vs \(Int(elo2))")
    print("  Win: Lagere ELO (\(Int(min(elo1, elo2)))) krijgt \(String(format: "%.1f", max(winShares.player1Share, winShares.player2Share) * 100))%")
    print("  Loss: Hogere ELO (\(Int(max(elo1, elo2)))) verliest \(String(format: "%.1f", max(lossShares.player1Share, lossShares.player2Share) * 100))%")
}

print("\n" + String(repeating: "=", count: 60))
print("VERWACHTE RESULTATEN:")
print("✅ Bij winst: Speler met LAGERE ELO krijgt MEER punten")
print("✅ Bij verlies: Speler met HOGERE ELO verliest MEER punten")
print("✅ Gavin (1192) zou meer moeten krijgen dan Joost (1224)")
print("✅ Shares moeten altijd optellen tot 1.0")

// Valideer dat shares optellen tot 1.0
let winSum = team1Shares.player1Share + team1Shares.player2Share
let lossSum = team2Shares.player1Share + team2Shares.player2Share

print("\nSHARE VALIDATIE:")
print("Win shares sum: \(String(format: "%.6f", winSum)) \(abs(winSum - 1.0) < 0.000001 ? "✅" : "❌")")
print("Loss shares sum: \(String(format: "%.6f", lossSum)) \(abs(lossSum - 1.0) < 0.000001 ? "✅" : "❌")")

print("\nNEXT STEPS:")
print("🚀 Test de app met een nieuwe Mix & Match wedstrijd")
print("🚀 Controleer dat Gavin meer punten krijgt dan Joost bij winst")
print("🚀 Controleer dat de verdeling logisch is voor alle games")

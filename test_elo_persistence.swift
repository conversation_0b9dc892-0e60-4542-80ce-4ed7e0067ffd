import Foundation

// Test om te valideren dat ELO changes correct worden gepersisteerd
// in zowel JSON als CloudKit

print("=== ELO PERSISTENCE TEST ===")
print("<PERSON><PERSON><PERSON> changes moeten worden opgeslagen en geladen in JSON en CloudKit")

// Mock data structures om JSON encoding/decoding te testen
struct MockGame: Codable {
    let id: UUID
    let gameNumber: Int
    var eloChanges: [UUID: Double]
    
    private enum CodingKeys: String, CodingKey {
        case id, gameNumber, eloChanges
    }
    
    init(id: UUID, gameNumber: Int, eloChanges: [UUID: Double]) {
        self.id = id
        self.gameNumber = gameNumber
        self.eloChanges = eloChanges
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        id = try container.decode(UUID.self, forKey: .id)
        gameNumber = try container.decode(Int.self, forKey: .gameNumber)
        
        // Decode ELO changes (convert from [String: Double] to [UUID: Double])
        if let eloChangesStringKeys = try container.decodeIfPresent([String: Double].self, forKey: .eloChanges) {
            eloChanges = Dictionary(uniqueKeysWithValues: eloChangesStringKeys.compactMap { (key, value) in
                guard let uuid = UUID(uuidString: key) else { return nil }
                return (uuid, value)
            })
        } else {
            eloChanges = [:]
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        try container.encode(id, forKey: .id)
        try container.encode(gameNumber, forKey: .gameNumber)
        
        // Encode ELO changes (convert from [UUID: Double] to [String: Double])
        let eloChangesStringKeys = Dictionary(uniqueKeysWithValues: eloChanges.map { (key, value) in
            (key.uuidString, value)
        })
        try container.encode(eloChangesStringKeys, forKey: .eloChanges)
    }
}

// Test data
let joostId = UUID()
let richardId = UUID()
let dariusId = UUID()
let gavinId = UUID()

let originalEloChanges: [UUID: Double] = [
    joostId: +2.75,
    richardId: +3.25,
    dariusId: -3.12,
    gavinId: -2.88
]

let originalGame = MockGame(
    id: UUID(),
    gameNumber: 1,
    eloChanges: originalEloChanges
)

print("\n" + String(repeating: "=", count: 60))
print("ORIGINAL GAME DATA:")
print("Game ID: \(originalGame.id)")
print("Game Number: \(originalGame.gameNumber)")
print("ELO Changes:")
for (playerId, change) in originalGame.eloChanges {
    let sign = change >= 0 ? "+" : ""
    print("  \(playerId): \(sign)\(change)")
}

// Test JSON encoding/decoding
print("\n" + String(repeating: "=", count: 60))
print("JSON PERSISTENCE TEST:")

do {
    // Encode to JSON
    let encoder = JSONEncoder()
    encoder.outputFormatting = .prettyPrinted
    let jsonData = try encoder.encode(originalGame)
    let jsonString = String(data: jsonData, encoding: .utf8) ?? "Failed to convert to string"
    
    print("Encoded JSON:")
    print(jsonString)
    
    // Decode from JSON
    let decoder = JSONDecoder()
    let decodedGame = try decoder.decode(MockGame.self, from: jsonData)
    
    print("\nDecoded Game Data:")
    print("Game ID: \(decodedGame.id)")
    print("Game Number: \(decodedGame.gameNumber)")
    print("ELO Changes:")
    for (playerId, change) in decodedGame.eloChanges {
        let sign = change >= 0 ? "+" : ""
        print("  \(playerId): \(sign)\(change)")
    }
    
    // Verify data integrity
    print("\n" + String(repeating: "-", count: 40))
    print("DATA INTEGRITY CHECK:")
    
    let idsMatch = originalGame.id == decodedGame.id
    let gameNumbersMatch = originalGame.gameNumber == decodedGame.gameNumber
    let eloChangesMatch = originalGame.eloChanges == decodedGame.eloChanges
    
    print("IDs match: \(idsMatch ? "✅" : "❌")")
    print("Game numbers match: \(gameNumbersMatch ? "✅" : "❌")")
    print("ELO changes match: \(eloChangesMatch ? "✅" : "❌")")
    
    if idsMatch && gameNumbersMatch && eloChangesMatch {
        print("\n🎉 JSON PERSISTENCE TEST PASSED!")
        print("✅ ELO changes are correctly saved and loaded from JSON")
    } else {
        print("\n❌ JSON PERSISTENCE TEST FAILED!")
        print("❌ Data was corrupted during encoding/decoding")
    }
    
} catch {
    print("❌ JSON encoding/decoding failed: \(error)")
}

print("\n" + String(repeating: "=", count: 60))
print("IMPLEMENTATION SUMMARY:")
print("✅ Added 'eloChanges' to CodingKeys enum")
print("✅ Updated init(from decoder:) to decode ELO changes")
print("✅ Updated encode(to encoder:) to encode ELO changes")
print("✅ UUID keys converted to String for JSON compatibility")
print("✅ CloudKit support was already implemented correctly")

print("\nNEXT STEPS:")
print("🚀 Test the app with a new Mix & Match match")
print("🚀 Complete a game and check if ELO changes appear")
print("🚀 Restart the app and verify ELO changes persist")
print("🚀 Click on game cards to see ELO changes under graphs")

print("\nEXPECTED BEHAVIOR:")
print("- New Mix & Match games will save ELO changes")
print("- Game cards will show ELO changes under score graphs")
print("- ELO changes will persist after app restart")
print("- Both JSON (local) and CloudKit (sync) will work")

<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "A1BC5BEE-841E-4AD4-BC78-7C2F6A57BA84"
   type = "1"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E8EB0412-3945-4007-B409-AFF598E68524"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "TableTennis/Models/Match.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "107"
            endingLineNumber = "107"
            landmarkName = "team1EloRating"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "81604E0E-**************-D194CCEF6F89"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "TableTennis/Views/Player/PlayerDetailView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "280"
            endingLineNumber = "280"
            landmarkName = "calculateEloChange(for:in:currentRating:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>

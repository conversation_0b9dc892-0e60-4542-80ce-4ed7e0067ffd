import Foundation

// Test om te valideren dat ELO changes correct worden weergegeven
// onder de score grafiek voor Mix & Match games

print("=== GAME ELO DISPLAY TEST ===")
print("<PERSON><PERSON><PERSON> changes moeten worden getoond onder score grafiek voor Mix & Match")

// Mock data structures
struct MockPlayer {
    let id: UUID
    let name: String
}

struct MockGame {
    let gameNumber: Int
    let team1Score: Int
    let team2Score: Int
    let eloChanges: [UUID: Double]
    let mixMatchTeam1Players: [MockPlayer]?
    let mixMatchTeam2Players: [MockPlayer]?
}

struct MockMatch {
    let type: MatchType
}

enum MatchType {
    case singles, doubles, mixMatch
}

// Test spelers
let joost = MockPlayer(id: UUID(), name: "<PERSON><PERSON>")
let richard = MockPlayer(id: UUID(), name: "<PERSON>")
let darius = MockPlayer(id: UUID(), name: "<PERSON>")
let gavin = MockPlayer(id: UUID(), name: "<PERSON>")

// Test Mix & Match game met ELO changes
let mixMatchGame = MockGame(
    gameNumber: 1,
    team1Score: 11,
    team2Score: 5,
    eloChanges: [
        joost.id: +2.75,    // Joost won maar kreeg minder omdat Richard lagere ELO heeft
        richard.id: +3.25,  // Richard won en kreeg meer omdat Joost hogere ELO heeft
        darius.id: -3.12,   // Darius verloor en verloor meer omdat hij hogere ELO heeft
        gavin.id: -2.88     // Gavin verloor maar verloor minder omdat hij lagere ELO heeft
    ],
    mixMatchTeam1Players: [joost, richard],
    mixMatchTeam2Players: [darius, gavin]
)

// Test normale game (geen ELO changes)
let normalGame = MockGame(
    gameNumber: 1,
    team1Score: 11,
    team2Score: 7,
    eloChanges: [:],
    mixMatchTeam1Players: nil,
    mixMatchTeam2Players: nil
)

let mixMatchMatch = MockMatch(type: .mixMatch)
let doublesMatch = MockMatch(type: .doubles)

// Simuleer GameEloChangeRow logica
func formatEloChange(_ eloChange: Double) -> String {
    if eloChange > 0 {
        return "+\(Int(eloChange.rounded()))"
    } else if eloChange < 0 {
        return "\(Int(eloChange.rounded()))"
    } else {
        return "0"
    }
}

func getEloChangeColor(_ eloChange: Double) -> String {
    if eloChange > 0 {
        return "green"
    } else if eloChange < 0 {
        return "red"
    } else {
        return "gray"
    }
}

print("\n" + String(repeating: "=", count: 60))
print("TEST RESULTS")
print(String(repeating: "=", count: 60))

print("MIX & MATCH GAME:")
print("Game \(mixMatchGame.gameNumber): \(mixMatchGame.team1Score) - \(mixMatchGame.team2Score)")
print("Team 1 (Blue): Joost & Richard")
print("Team 2 (Red): Darius & Gavin")
print()

if mixMatchMatch.type == .mixMatch && !mixMatchGame.eloChanges.isEmpty {
    print("ELO CHANGES:")
    
    // Team 1 spelers
    if let team1Players = mixMatchGame.mixMatchTeam1Players {
        for player in team1Players {
            let eloChange = mixMatchGame.eloChanges[player.id] ?? 0.0
            let changeText = formatEloChange(eloChange)
            let color = getEloChangeColor(eloChange)
            print("  🔵 \(player.name): \(changeText) (\(color))")
        }
    }
    
    // Team 2 spelers
    if let team2Players = mixMatchGame.mixMatchTeam2Players {
        for player in team2Players {
            let eloChange = mixMatchGame.eloChanges[player.id] ?? 0.0
            let changeText = formatEloChange(eloChange)
            let color = getEloChangeColor(eloChange)
            print("  🔴 \(player.name): \(changeText) (\(color))")
        }
    }
} else {
    print("ELO CHANGES: Niet getoond (geen Mix & Match of geen ELO data)")
}

print("\nNORMALE GAME (Doubles):")
print("Game \(normalGame.gameNumber): \(normalGame.team1Score) - \(normalGame.team2Score)")

if doublesMatch.type == .mixMatch && !normalGame.eloChanges.isEmpty {
    print("ELO CHANGES: Getoond")
} else {
    print("ELO CHANGES: Niet getoond (geen Mix & Match of geen ELO data)")
}

print("\n" + String(repeating: "=", count: 60))
print("UI LAYOUT VERWACHTING:")
print("1. Score grafiek wordt getoond (bestaande functionaliteit)")
print("2. Voor Mix & Match games met ELO data:")
print("   - 'ELO Changes' header")
print("   - Team 1 spelers met blauwe cirkel indicator")
print("   - Team 2 spelers met rode cirkel indicator")
print("   - Elke speler: naam + ELO change met kleur")
print("   - Groene pijl omhoog voor positieve changes")
print("   - Rode pijl omlaag voor negatieve changes")
print("3. Voor andere match types: geen ELO sectie")

print("\n" + String(repeating: "=", count: 60))
print("IMPLEMENTATIE DETAILS:")
print("✅ GameScoreInlineView uitgebreid met ELO sectie")
print("✅ Nieuwe GameEloChangeRow component")
print("✅ Alleen getoond voor match.type == .mixMatch")
print("✅ Alleen getoond als game.eloChanges niet leeg is")
print("✅ Team kleuren: blauw voor team 1, rood voor team 2")
print("✅ ELO change kleuren: groen (+), rood (-), grijs (0)")
print("✅ Consistent met bestaande ELO change formatting")

print("\nVOLGENDE STAPPEN:")
print("🚀 Test de app met een echte Mix & Match wedstrijd")
print("🚀 Klik op een game card om de inline view te zien")
print("🚀 Controleer dat ELO changes correct worden weergegeven")
print("🚀 Controleer dat normale wedstrijden geen ELO sectie tonen")
